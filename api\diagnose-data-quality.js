#!/usr/bin/env node

/**
 * TrakSong Data Quality Diagnostic Script
 * 
 * This script helps identify and optionally fix data quality issues
 * in the TrakSong database.
 * 
 * Usage:
 *   node scripts/diagnose-data-quality.js [--fix]
 * 
 * Options:
 *   --fix    Attempt to automatically fix simple issues
 *   --report Generate detailed report only (default)
 */

const mongoose = require('mongoose');
const path = require('path');

// Load environment variables
require('dotenv').config({ path: path.join(__dirname, '../api/.env') });

// Import Track model
const Track = require('./models/Track.model');

class DataQualityDiagnostic {
  constructor() {
    this.issues = {
      critical: [],
      warnings: [],
      compliance: []
    };
    this.stats = {
      total: 0,
      valid: 0,
      invalid: 0,
      fixed: 0
    };
  }

  async connect() {
    try {
      await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/traksong');
      console.log('✅ Connected to MongoDB');
    } catch (error) {
      console.error('❌ Failed to connect to MongoDB:', error.message);
      process.exit(1);
    }
  }

  async disconnect() {
    await mongoose.disconnect();
    console.log('📤 Disconnected from MongoDB');
  }

  async analyzeDataQuality() {
    console.log('🔍 Analyzing data quality...\n');

    const tracks = await Track.find({});
    this.stats.total = tracks.length;

    console.log(`📊 Found ${tracks.length} tracks to analyze\n`);

    for (const track of tracks) {
      await this.analyzeTrack(track);
    }

    this.generateReport();
  }

  async analyzeTrack(track) {
    let hasIssues = false;

    // Check critical fields
    if (!track.title) {
      this.issues.critical.push({
        type: 'missing_title',
        trackId: track._id,
        message: `Track ${track._id} is missing title`
      });
      hasIssues = true;
    }

    if (!track.artist) {
      this.issues.critical.push({
        type: 'missing_artist',
        trackId: track._id,
        message: `Track ${track._id} is missing artist`
      });
      hasIssues = true;
    }

    if (!track.filePath) {
      this.issues.critical.push({
        type: 'missing_file_path',
        trackId: track._id,
        message: `Track ${track._id} is missing file path`
      });
      hasIssues = true;
    }

    if (!track.duration || track.duration <= 0) {
      this.issues.critical.push({
        type: 'invalid_duration',
        trackId: track._id,
        message: `Track ${track._id} has invalid duration: ${track.duration}`
      });
      hasIssues = true;
    }

    // Check compliance fields
    if (!track.samroWorkNumber) {
      this.issues.compliance.push({
        type: 'missing_samro_work_number',
        trackId: track._id,
        message: `Track ${track._id} missing SAMRO work number`
      });
    }

    if (!track.composers?.some(c => c.ipiNumber)) {
      this.issues.compliance.push({
        type: 'missing_composer_ipi',
        trackId: track._id,
        message: `Track ${track._id} missing composer IPI numbers`
      });
    }

    if (!track.sampraArtistNumbers?.length) {
      this.issues.compliance.push({
        type: 'missing_sampra_artist_numbers',
        trackId: track._id,
        message: `Track ${track._id} missing SAMPRA artist numbers`
      });
    }

    // Update stats
    if (hasIssues) {
      this.stats.invalid++;
    } else {
      this.stats.valid++;
    }
  }

  generateReport() {
    console.log('📋 DATA QUALITY REPORT');
    console.log('='.repeat(50));
    console.log(`📊 Total Tracks: ${this.stats.total}`);
    console.log(`✅ Valid Tracks: ${this.stats.valid}`);
    console.log(`❌ Invalid Tracks: ${this.stats.invalid}`);
    console.log(`🔧 Fixed Tracks: ${this.stats.fixed}`);
    
    const qualityScore = this.stats.total > 0 ? 
      Math.round((this.stats.valid / this.stats.total) * 100) : 100;
    console.log(`📈 Quality Score: ${qualityScore}%\n`);

    // Critical Issues
    if (this.issues.critical.length > 0) {
      console.log('🚨 CRITICAL ISSUES (Prevent Playback):');
      console.log('-'.repeat(40));
      
      const criticalByType = this.groupIssuesByType(this.issues.critical);
      for (const [type, issues] of Object.entries(criticalByType)) {
        console.log(`  ${this.getIssueIcon(type)} ${this.formatIssueType(type)}: ${issues.length} tracks`);
      }
      console.log();
    }

    // Compliance Issues
    if (this.issues.compliance.length > 0) {
      console.log('⚠️  COMPLIANCE ISSUES (Legal/Regulatory):');
      console.log('-'.repeat(40));
      
      const complianceByType = this.groupIssuesByType(this.issues.compliance);
      for (const [type, issues] of Object.entries(complianceByType)) {
        console.log(`  ${this.getIssueIcon(type)} ${this.formatIssueType(type)}: ${issues.length} tracks`);
      }
      console.log();
    }

    // Recommendations
    this.generateRecommendations();
  }

  groupIssuesByType(issues) {
    return issues.reduce((acc, issue) => {
      if (!acc[issue.type]) acc[issue.type] = [];
      acc[issue.type].push(issue);
      return acc;
    }, {});
  }

  getIssueIcon(type) {
    const icons = {
      missing_title: '📝',
      missing_artist: '👤',
      missing_file_path: '📁',
      invalid_duration: '⏱️',
      missing_samro_work_number: '🎵',
      missing_composer_ipi: '👨‍🎤',
      missing_sampra_artist_numbers: '🎤'
    };
    return icons[type] || '❓';
  }

  formatIssueType(type) {
    const formats = {
      missing_title: 'Missing Track Titles',
      missing_artist: 'Missing Artist Information',
      missing_file_path: 'Missing File Paths',
      invalid_duration: 'Invalid Duration',
      missing_samro_work_number: 'Missing SAMRO Work Numbers',
      missing_composer_ipi: 'Missing Composer IPI Numbers',
      missing_sampra_artist_numbers: 'Missing SAMPRA Artist Numbers'
    };
    return formats[type] || type.replace(/_/g, ' ').toUpperCase();
  }

  generateRecommendations() {
    console.log('💡 RECOMMENDATIONS:');
    console.log('-'.repeat(40));

    if (this.issues.critical.length > 0) {
      console.log('  1. 🚨 Fix critical issues first - these prevent tracks from playing');
      console.log('     • Use the Data Quality Dashboard at /data-quality');
      console.log('     • Update missing titles, artists, and file paths');
      console.log('     • Verify audio files exist and are accessible');
    }

    if (this.issues.compliance.length > 0) {
      console.log('  2. ⚖️  Address compliance issues for legal requirements');
      console.log('     • Gather SAMRO work registration numbers');
      console.log('     • Collect composer and artist IPI numbers');
      console.log('     • Ensure proper rights attribution');
    }

    console.log('  3. 🔄 Set up regular monitoring');
    console.log('     • Enable real-time compliance monitoring');
    console.log('     • Schedule weekly data quality reports');
    console.log('     • Train users on proper metadata entry');

    console.log('\n📖 For detailed guidance, see: DATA_QUALITY_GUIDE.md');
  }

  async fixSimpleIssues() {
    console.log('🔧 Attempting to fix simple issues...\n');

    // This is a basic example - in practice, you'd need more sophisticated logic
    for (const issue of this.issues.critical) {
      if (issue.type === 'invalid_duration') {
        try {
          // For demo purposes, set a default duration
          // In practice, you'd calculate from the audio file
          await Track.findByIdAndUpdate(issue.trackId, {
            duration: 180, // 3 minutes default
            updatedAt: new Date()
          });
          
          console.log(`✅ Fixed duration for track ${issue.trackId}`);
          this.stats.fixed++;
        } catch (error) {
          console.log(`❌ Failed to fix track ${issue.trackId}: ${error.message}`);
        }
      }
    }

    console.log(`\n🔧 Fixed ${this.stats.fixed} issues automatically`);
    console.log('⚠️  Manual intervention required for remaining issues\n');
  }
}

async function main() {
  const args = process.argv.slice(2);
  const shouldFix = args.includes('--fix');

  console.log('🎵 TrakSong Data Quality Diagnostic Tool');
  console.log('=' .repeat(50));
  console.log(`Mode: ${shouldFix ? 'Fix Issues' : 'Report Only'}\n`);

  const diagnostic = new DataQualityDiagnostic();

  try {
    await diagnostic.connect();
    await diagnostic.analyzeDataQuality();

    if (shouldFix) {
      await diagnostic.fixSimpleIssues();
    }

  } catch (error) {
    console.error('❌ Error during analysis:', error.message);
  } finally {
    await diagnostic.disconnect();
  }
}

// Run the script
if (require.main === module) {
  main().catch(console.error);
}

module.exports = DataQualityDiagnostic;
