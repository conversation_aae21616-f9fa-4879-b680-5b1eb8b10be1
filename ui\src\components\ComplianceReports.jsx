import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  MenuItem,
  Grid,
  Alert,
  Pagination,
  Tooltip,
  CircularProgress,
  Tabs,
  Tab,
  FormControl,
  InputLabel,
  Select,
  FormControlLabel,
  Checkbox,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Switch,
  Autocomplete,
  Stack,
  Badge
} from '@mui/material';
import {
  Add as AddIcon,
  Visibility as ViewIcon,
  Download as DownloadIcon,
  Verified as VerifiedIcon,
  Pending as PendingIcon,
  Error as ErrorIcon,
  FilterList as FilterIcon,
  GetApp as ExportIcon,
  Assessment as ReportIcon,
  Schedule as ScheduleIcon,
  LocationOn as LocationIcon,
  MusicNote as MusicIcon,
  Person as PersonIcon,
  Business as BusinessIcon,
  ExpandMore as ExpandMoreIcon,
  Clear as ClearIcon,
  Search as SearchIcon,
  DateRange as DateRangeIcon,
  TrendingUp as TrendingUpIcon,
  PieChart as PieChartIcon,
  BarChart as BarChartIcon
} from '@mui/icons-material';

import { useAuth } from '../context/AuthContext';
import { complianceService } from '../services/api';

const ComplianceReports = () => {
  const [reports, setReports] = useState([]);
  const [loading, setLoading] = useState(true);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [activeTab, setActiveTab] = useState(0);

  // Enhanced filters
  const [filters, setFilters] = useState({
    status: '',
    reportType: '',
    organization: '',
    dateRange: {
      startDate: '',
      endDate: ''
    },
    storeIds: [],
    trackIds: [],
    artists: [],
    composers: [],
    publishers: [],
    recordLabels: [],
    location: {
      city: '',
      province: ''
    },
    playbackStatus: '',
    minDuration: '',
    maxDuration: '',
    includeISRC: false,
    includeISWC: false
  });

  // Dialog states
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [viewDialogOpen, setViewDialogOpen] = useState(false);
  const [selectedReport, setSelectedReport] = useState(null);
  const [exportDialogOpen, setExportDialogOpen] = useState(false);

  // Bulk export states
  const [selectedReports, setSelectedReports] = useState([]);
  const [bulkExportFormats, setBulkExportFormats] = useState(['csv']);
  const [bulkExporting, setBulkExporting] = useState(false);

  // Individual export states - tracks which reports are being exported
  const [exportingReports, setExportingReports] = useState({});

  // View report loading state
  const [viewingReport, setViewingReport] = useState(null);

  // Report generation state - Set default date range to last 7 days to capture recent plays
  const [newReport, setNewReport] = useState({
    reportType: 'weekly',
    dateRange: {
      startDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 7 days ago
      endDate: new Date()
    },
    organization: '',
    storeIds: [],
    includeFields: {
      trackDetails: true,
      playbackDetails: true,
      locationDetails: true,
      rightsHolderInfo: true,
      complianceInfo: true,
      aggregatedData: false
    },
    exportFormats: ['csv'],
    customFields: []
  });

  // Helper function to safely format dates
  const formatDate = (dateValue, options = {}) => {
    if (!dateValue) return 'N/A';
    try {
      const date = new Date(dateValue);
      if (isNaN(date.getTime())) return 'Invalid Date';
      return options.includeTime ? date.toLocaleString() : date.toLocaleDateString();
    } catch (error) {
      console.error('Error formatting date:', error);
      return 'Invalid Date';
    }
  };

  // Helper function to safely format date for input fields
  const formatDateForInput = (dateValue) => {
    if (!dateValue) return '';
    try {
      const date = new Date(dateValue);
      if (isNaN(date.getTime())) return '';
      return date.toISOString().split('T')[0];
    } catch (error) {
      console.error('Error formatting date for input:', error);
      return '';
    }
  };

  const [generating, setGenerating] = useState(false);
  const [availableStores, setAvailableStores] = useState([]);
  const [availableArtists, setAvailableArtists] = useState([]);
  const [availableComposers, setAvailableComposers] = useState([]);
  const [availablePublishers, setAvailablePublishers] = useState([]);

  // Aggregated data states
  const [aggregatedData, setAggregatedData] = useState(null);
  const [analyticsLoading, setAnalyticsLoading] = useState(false);


  const { user, isSAMRO, isSAMPRA, isComplianceAdmin, hasPermission } = useAuth();

  useEffect(() => {
    fetchReports();
    fetchFilterOptions();
  }, [page, filters]);

  useEffect(() => {
    console.log('Tab changed to:', activeTab);
    if (activeTab === 1) {
      console.log('Fetching aggregated data...');
      fetchAggregatedData();
    }
  }, [activeTab]);

  const fetchReports = async () => {
    try {
      setLoading(true);
      const params = {
        page,
        limit: 10,
        ...filters,
        // Flatten nested objects for API
        ...(filters.dateRange.startDate && { startDate: filters.dateRange.startDate }),
        ...(filters.dateRange.endDate && { endDate: filters.dateRange.endDate }),
        ...(filters.location.city && { city: filters.location.city }),
        ...(filters.location.province && { province: filters.location.province })
      };

      const response = await complianceService.getReports(params);

      // Handle different response structures
      const reportsData = response.data?.reports || response.data?.data || [];
      const paginationData = response.data?.pagination || { pages: 1, total: 0 };

      console.log('Compliance reports fetched:', {
        total: reportsData.length,
        pagination: paginationData,
        filters: params
      });

      setReports(Array.isArray(reportsData) ? reportsData : []);
      setTotalPages(paginationData.pages || 1);
    } catch (error) {
      console.error('Error fetching reports:', error);
      setReports([]);
      setTotalPages(1);
    } finally {
      setLoading(false);
    }
  };

  const fetchFilterOptions = async () => {
    try {
      // Fetch available stores, artists, composers, publishers for filters
      const [storesRes, artistsRes, composersRes, publishersRes] = await Promise.all([
        complianceService.getAvailableStores(),
        complianceService.getAvailableArtists(),
        complianceService.getAvailableComposers(),
        complianceService.getAvailablePublishers()
      ]);

      setAvailableStores(storesRes.data || []);
      setAvailableArtists(artistsRes.data || []);
      setAvailableComposers(composersRes.data || []);
      setAvailablePublishers(publishersRes.data || []);
    } catch (error) {
      console.error('Error fetching filter options:', error);
    }
  };





  const fetchAggregatedData = async () => {
    try {
      setAnalyticsLoading(true);

      // Get date range for last 30 days
      const endDate = new Date();
      const startDate = new Date();
      startDate.setDate(endDate.getDate() - 30);

      const params = {
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
        limit: 50
      };

      console.log('Fetching aggregated data with params:', params);

      const [topTracks, rightsHolders, geoData] = await Promise.all([
        complianceService.getTopTracks(params),
        complianceService.getUsageByRightsHolder(params),
        complianceService.getGeographicalData(params)
      ]);

      console.log('Aggregated API responses:', {
        topTracks: topTracks.data,
        rightsHolders: rightsHolders.data,
        geoData: geoData.data
      });

      // Process top tracks data
      const processedTopTracks = [];
      if (topTracks.data?.data && Array.isArray(topTracks.data.data)) {
        topTracks.data.data.forEach(track => {
          processedTopTracks.push({
            title: track.title || 'Unknown Title',
            artist: track.artist || 'Unknown Artist',
            isrcCode: track.isrcCode,
            iswcCode: track.iswcCode,
            plays: track.playCount || 0,
            duration: track.duration || 0,
            totalDuration: track.totalDuration || 0,
            compliance: track.compliance || {}
          });
        });
      }

      // Process rights holders data
      const processedRightsHolders = [];
      if (rightsHolders.data?.data && Array.isArray(rightsHolders.data.data)) {
        rightsHolders.data.data.forEach(holder => {
          // Determine type based on available data
          let type = holder.type;
          if (!type || type === 'Unknown') {
            // Try to infer type from name or role
            if (holder.role) {
              type = holder.role;
            } else if (holder.name) {
              const name = holder.name.toLowerCase();
              if (name.includes('publisher') || name.includes('publishing')) {
                type = 'Publisher';
              } else if (name.includes('composer') || name.includes('writer')) {
                type = 'Composer';
              } else if (name.includes('artist') || name.includes('performer')) {
                type = 'Artist';
              } else {
                type = 'Rights Holder';
              }
            } else {
              type = 'Rights Holder';
            }
          }

          processedRightsHolders.push({
            name: holder.name || holder._id || 'Unknown Rights Holder',
            type: type,
            totalPlays: holder.totalPlays || 0,
            totalDuration: (holder.totalDuration || 0) / 3600 // Convert to hours
          });
        });
      }

      // Process geographic data
      const processedGeoData = [];
      if (geoData.data && Array.isArray(geoData.data)) {
        geoData.data.forEach(location => {
          processedGeoData.push({
            province: location.province || 'Unknown Province',
            city: location.city || 'Unknown City',
            stores: location.uniqueStores || 0,
            totalPlays: location.playCount || 0
          });
        });
      }

      const aggregated = {
        topPerformingTracks: processedTopTracks,
        rightsHolderSummary: processedRightsHolders,
        geographicDistribution: processedGeoData
      };

      console.log('Processed aggregated data:', aggregated);
      setAggregatedData(aggregated);
    } catch (error) {
      console.error('Error fetching aggregated data:', error);
      console.error('Error details:', {
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        message: error.message
      });

      // Set empty data when API fails
      setAggregatedData({
        topPerformingTracks: [],
        rightsHolderSummary: [],
        geographicDistribution: []
      });
    } finally {
      setAnalyticsLoading(false);
    }
  };

  const handleCreateReport = async () => {
    try {
      setGenerating(true);

      // Enforce organization restrictions based on user role
      let organization;
      if (isSAMRO) {
        organization = 'SAMRO'; // SAMRO users can only generate SAMRO reports
      } else if (isSAMPRA) {
        organization = 'SAMPRA'; // SAMPRA users can only generate SAMPRA reports
      } else if (isComplianceAdmin) {
        organization = newReport.organization || 'All'; // Compliance admins can choose
      } else {
        organization = 'All'; // Other users default to All
      }

      const reportData = {
        ...newReport,
        organization,
        // Enhanced report configuration
        includeDetailedMetadata: true,
        includeRightsHolderData: true,
        includeComplianceVerification: true,
        includeGeographicalData: true,
        includeTimestampPrecision: 'millisecond'
      };

      console.log('Creating report with organization restriction:', {
        userRole: user.role,
        isSAMRO,
        isSAMPRA,
        isComplianceAdmin,
        requestedOrganization: newReport.organization,
        finalOrganization: organization
      });

      await complianceService.generateReport(reportData);
      setCreateDialogOpen(false);
      resetNewReport();
      fetchReports();
    } catch (error) {
      console.error('Error creating report:', error);
      // Show user-friendly error message
      alert('Failed to generate report. Please check your permissions and try again.');
    } finally {
      setGenerating(false);
    }
  };

  const resetNewReport = () => {
    // Set default organization based on user role
    const defaultOrganization = isSAMRO ? 'SAMRO' : isSAMPRA ? 'SAMPRA' : '';

    setNewReport({
      reportType: 'weekly',
      dateRange: {
        startDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 7 days ago
        endDate: new Date()
      },
      organization: defaultOrganization,
      storeIds: [],
      includeFields: {
        trackDetails: true,
        playbackDetails: true,
        locationDetails: true,
        rightsHolderInfo: true,
        complianceInfo: true,
        aggregatedData: false
      },
      exportFormats: ['csv'],
      customFields: []
    });
  };

  const handleExportReport = async (reportId, format = 'csv') => {
    try {
      console.log(`Starting export for report ${reportId} in ${format} format`);

      // Show loading state
      setExportingReports(prev => ({ ...prev, [reportId]: format }));

      const response = await complianceService.exportReport(reportId, format);
      console.log('Export response:', response.data);

      // Handle Excel format (returns blob directly)
      if (format === 'excel' && response.data instanceof Blob) {
        const blob = new Blob([response.data], {
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        });
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `compliance-report-${reportId}.xlsx`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);

        alert('Excel file downloaded successfully!');
      } else if (response.data.downloadUrl) {
        // Normalize the download URL to handle any backslashes
        const normalizedUrl = response.data.downloadUrl.replace(/\\\\/g, '/').replace(/([^:])\/\//g, '$1/');
        
        // Construct full URL if needed
        const downloadUrl = normalizedUrl.startsWith('http')
          ? normalizedUrl
          : `http://localhost:5000${normalizedUrl.startsWith('/') ? '' : '/'}${normalizedUrl}`;
          
        console.log('Normalized download URL:', downloadUrl);

        console.log('Downloading from URL:', downloadUrl);

        // Create a proper download instead of opening in new tab
        try {
          const downloadResponse = await fetch(downloadUrl, {
            method: 'GET',
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`,
            },
          });

          if (!downloadResponse.ok) {
            throw new Error(`Download failed: ${downloadResponse.status}`);
          }

          const blob = await downloadResponse.blob();
          const url = window.URL.createObjectURL(blob);
          const link = document.createElement('a');
          link.href = url;
          link.download = `compliance-report-${reportId}.${format === 'excel' ? 'xlsx' : format}`;
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          window.URL.revokeObjectURL(url);

          alert(`${format.toUpperCase()} file downloaded successfully!`);
        } catch (downloadError) {
          console.error('Direct download failed, falling back to window.open:', downloadError);
          // Fallback to opening in new tab
          window.open(downloadUrl, '_blank');
          alert(`${format.toUpperCase()} export started successfully! Check your downloads.`);
        }
      } else if (response.data instanceof Blob || response.data instanceof ArrayBuffer) {
        // Handle blob response for direct download
        const blob = new Blob([response.data], {
          type: format === 'csv' ? 'text/csv' :
                format === 'excel' ? 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' :
                format === 'pdf' ? 'application/pdf' : 'application/octet-stream'
        });
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `compliance-report-${reportId}.${format === 'excel' ? 'xlsx' : format}`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);

        alert(`${format.toUpperCase()} file downloaded successfully!`);
      } else {
        console.error('Unexpected response format:', response.data);
        alert('Export completed but download link not available. Please contact support.');
      }
    } catch (error) {
      console.error('Error exporting report:', error);

      // Show user-friendly error message
      const errorMessage = error.response?.data?.error || error.message || 'Unknown error occurred';

      // Special handling for Excel export errors
      if (format === 'excel' && errorMessage.includes('ExcelJS')) {
        alert(`Excel export failed: ExcelJS module not installed on server. Please contact administrator to install the exceljs package.`);
      } else {
        alert(`Failed to export report: ${errorMessage}`);
      }

      // Log detailed error for debugging
      console.error('Export error details:', {
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        reportId,
        format
      });
    } finally {
      // Clear loading state
      setExportingReports(prev => {
        const newState = { ...prev };
        delete newState[reportId];
        return newState;
      });
    }
  };

  const handleBulkExport = async () => {
    if (selectedReports.length === 0) {
      alert('Please select at least one report to export.');
      return;
    }

    if (bulkExportFormats.length === 0) {
      alert('Please select at least one export format.');
      return;
    }

    try {
      setBulkExporting(true);
      const response = await complianceService.bulkExportReports(selectedReports, bulkExportFormats);

      if (response.data.results && response.data.results.length > 0) {
        // Create download links for all exported files
        for (const result of response.data.results) {
          if (result.downloadUrl) {
            try {
              const downloadUrl = `http://localhost:5000${result.downloadUrl}`;
              const downloadResponse = await fetch(downloadUrl, {
                method: 'GET',
                headers: {
                  'Authorization': `Bearer ${localStorage.getItem('token')}`,
                },
              });

              if (downloadResponse.ok) {
                const blob = await downloadResponse.blob();
                const url = window.URL.createObjectURL(blob);
                const link = document.createElement('a');
                link.href = url;
                link.download = result.filename;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                window.URL.revokeObjectURL(url);
              } else {
                console.error(`Failed to download ${result.filename}`);
              }
            } catch (downloadError) {
              console.error(`Error downloading ${result.filename}:`, downloadError);
              // Fallback to direct link
              const link = document.createElement('a');
              link.href = `http://localhost:5000${result.downloadUrl}`;
              link.download = result.filename;
              link.click();
            }
          }
        }

        alert(`Bulk export completed successfully! ${response.data.summary.successful} files exported.`);
      }

      if (response.data.errors && response.data.errors.length > 0) {
        console.warn('Some exports failed:', response.data.errors);
        alert(`Export completed with ${response.data.errors.length} errors. Check console for details.`);
      }

      setExportDialogOpen(false);
      setSelectedReports([]);
      setBulkExportFormats(['csv']);
    } catch (error) {
      console.error('Error in bulk export:', error);
      alert('Failed to perform bulk export. Please try again.');
    } finally {
      setBulkExporting(false);
    }
  };

  const handleViewReport = async (reportId) => {
    try {
      console.log(`Fetching details for report ${reportId}`);

      // Show loading state
      setViewingReport(reportId);

      const response = await complianceService.getReport(reportId);
      console.log('Report details response:', response.data);

      setSelectedReport(response.data);
      setViewDialogOpen(true);
    } catch (error) {
      console.error('Error fetching report details:', error);

      // Show user-friendly error message
      const errorMessage = error.response?.data?.error || error.message || 'Unknown error occurred';
      alert(`Failed to load report details: ${errorMessage}`);

      // Log detailed error for debugging
      console.error('View report error details:', {
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        reportId
      });
    } finally {
      // Clear loading state
      setViewingReport(null);
    }
  };

  const clearFilters = () => {
    setFilters({
      status: '',
      reportType: '',
      organization: '',
      dateRange: {
        startDate: '',
        endDate: ''
      },
      storeIds: [],
      trackIds: [],
      artists: [],
      composers: [],
      publishers: [],
      recordLabels: [],
      location: {
        city: '',
        province: ''
      },
      playbackStatus: '',
      minDuration: '',
      maxDuration: '',
      includeISRC: false,
      includeISWC: false
    });
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'verified':
        return <VerifiedIcon color="success" />;
      case 'pending':
        return <PendingIcon color="warning" />;
      case 'rejected':
        return <ErrorIcon color="error" />;
      default:
        return <PendingIcon color="disabled" />;
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'verified':
        return 'success';
      case 'pending':
        return 'warning';
      case 'rejected':
        return 'error';
      default:
        return 'default';
    }
  };

  // SAMRO and SAMPRA staff can generate reports for their own organizations
  const canCreateReports = hasPermission('export_compliance_data') || isComplianceAdmin || isSAMRO || isSAMPRA;
  const canVerifyReports = hasPermission('data_verification') || isComplianceAdmin;

  return (
    <Box sx={{
      width: '100%',
      maxWidth: '100%',
      overflow: 'hidden',
      minWidth: 0
    }}>
      {/* Header */}
      <Box sx={{
        display: 'flex',
        flexDirection: { xs: 'column', sm: 'row' },
        justifyContent: 'space-between',
        alignItems: { xs: 'flex-start', sm: 'center' },
        mb: 3,
        gap: 2
      }}>
        <Box>
          <Typography variant="h4" sx={{ fontWeight: 700 }}>
            {isSAMRO ? 'SAMRO' : isSAMPRA ? 'SAMPRA' : 'Compliance'} Reports
          </Typography>
          <Typography variant="subtitle1" color="text.secondary">
            Comprehensive music rights data collection and reporting
          </Typography>
        </Box>
        <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2} sx={{ width: { xs: '100%', sm: 'auto' } }}>
          {canCreateReports && (
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={() => setCreateDialogOpen(true)}
              sx={{
                bgcolor: isSAMRO ? 'primary.main' : isSAMPRA ? 'secondary.main' : 'success.main',
                '&:hover': {
                  bgcolor: isSAMRO ? 'primary.dark' : isSAMPRA ? 'secondary.dark' : 'success.dark'
                }
              }}
              fullWidth={{ xs: true, sm: false }}
            >
              Generate Report
            </Button>
          )}
          <Button
            variant="outlined"
            startIcon={<FilterIcon />}
            onClick={clearFilters}
            color="inherit"
            fullWidth={{ xs: true, sm: false }}
          >
            Clear Filters
          </Button>

        </Stack>
      </Box>

      {/* Tabs */}
      <Card sx={{ mb: 3 }}>
        <Tabs
          value={activeTab}
          onChange={(e, newValue) => setActiveTab(newValue)}
          sx={{ borderBottom: 1, borderColor: 'divider' }}
        >
          <Tab
            icon={<ReportIcon />}
            label="All Reports"
            iconPosition="start"
          />
          <Tab
            icon={<BarChartIcon />}
            label="Aggregated Data"
            iconPosition="start"
          />
        </Tabs>
      </Card>

      {/* Enhanced Filters */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 2, display: 'flex', alignItems: 'center', gap: 1 }}>
            <FilterIcon />
            Advanced Filters
          </Typography>

          <Accordion>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography variant="subtitle1">Basic Filters</Typography>
            </AccordionSummary>
            <AccordionDetails>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={3}>
                  <TextField
                    select
                    fullWidth
                    label="Status"
                    value={filters.status}
                    onChange={(e) => setFilters({ ...filters, status: e.target.value })}
                  >
                    <MenuItem value="">All Statuses</MenuItem>
                    <MenuItem value="pending">Pending</MenuItem>
                    <MenuItem value="verified">Verified</MenuItem>
                    <MenuItem value="rejected">Rejected</MenuItem>
                    <MenuItem value="requires_review">Requires Review</MenuItem>
                  </TextField>
                </Grid>
                <Grid item xs={12} sm={3}>
                  <TextField
                    select
                    fullWidth
                    label="Report Type"
                    value={filters.reportType}
                    onChange={(e) => setFilters({ ...filters, reportType: e.target.value })}
                  >
                    <MenuItem value="">All Types</MenuItem>
                    <MenuItem value="daily">Daily</MenuItem>
                    <MenuItem value="weekly">Weekly</MenuItem>
                    <MenuItem value="monthly">Monthly</MenuItem>
                    <MenuItem value="quarterly">Quarterly</MenuItem>
                    <MenuItem value="annual">Annual</MenuItem>
                    <MenuItem value="custom">Custom Range</MenuItem>
                  </TextField>
                </Grid>
                <Grid item xs={12} sm={3}>
                  <TextField
                    select
                    fullWidth
                    label="Organization"
                    value={filters.organization}
                    onChange={(e) => setFilters({ ...filters, organization: e.target.value })}
                    disabled={!isComplianceAdmin}
                  >
                    <MenuItem value="">All Organizations</MenuItem>
                    <MenuItem value="SAMRO">SAMRO</MenuItem>
                    <MenuItem value="SAMPRA">SAMPRA</MenuItem>
                    <MenuItem value="RISA">RISA</MenuItem>
                  </TextField>
                </Grid>
                <Grid item xs={12} sm={3}>
                  <TextField
                    select
                    fullWidth
                    label="Playback Status"
                    value={filters.playbackStatus}
                    onChange={(e) => setFilters({ ...filters, playbackStatus: e.target.value })}
                  >
                    <MenuItem value="">All Playback</MenuItem>
                    <MenuItem value="completed">Completed</MenuItem>
                    <MenuItem value="skipped">Skipped</MenuItem>
                    <MenuItem value="interrupted">Interrupted</MenuItem>
                    <MenuItem value="partial">Partial</MenuItem>
                  </TextField>
                </Grid>
              </Grid>
            </AccordionDetails>
          </Accordion>

          <Accordion>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography variant="subtitle1">Date Range & Duration</Typography>
            </AccordionSummary>
            <AccordionDetails>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={3}>
                  <TextField
                    type="date"
                    fullWidth
                    label="Start Date"
                    value={filters.dateRange.startDate}
                    onChange={(e) => setFilters({
                      ...filters,
                      dateRange: { ...filters.dateRange, startDate: e.target.value }
                    })}
                    InputLabelProps={{ shrink: true }}
                  />
                </Grid>
                <Grid item xs={12} sm={3}>
                  <TextField
                    type="date"
                    fullWidth
                    label="End Date"
                    value={filters.dateRange.endDate}
                    onChange={(e) => setFilters({
                      ...filters,
                      dateRange: { ...filters.dateRange, endDate: e.target.value }
                    })}
                    InputLabelProps={{ shrink: true }}
                  />
                </Grid>
                <Grid item xs={12} sm={3}>
                  <TextField
                    type="number"
                    fullWidth
                    label="Min Duration (seconds)"
                    value={filters.minDuration}
                    onChange={(e) => setFilters({ ...filters, minDuration: e.target.value })}
                  />
                </Grid>
                <Grid item xs={12} sm={3}>
                  <TextField
                    type="number"
                    fullWidth
                    label="Max Duration (seconds)"
                    value={filters.maxDuration}
                    onChange={(e) => setFilters({ ...filters, maxDuration: e.target.value })}
                  />
                </Grid>
              </Grid>
            </AccordionDetails>
          </Accordion>

          <Accordion>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography variant="subtitle1">Rights Holders & Metadata</Typography>
            </AccordionSummary>
            <AccordionDetails>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <Autocomplete
                    multiple
                    options={availableArtists}
                    value={filters.artists}
                    onChange={(e, newValue) => setFilters({ ...filters, artists: newValue })}
                    renderInput={(params) => (
                      <TextField {...params} label="Artists" placeholder="Select artists" />
                    )}
                    renderTags={(value, getTagProps) =>
                      value.map((option, index) => (
                        <Chip variant="outlined" label={option} {...getTagProps({ index })} />
                      ))
                    }
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Autocomplete
                    multiple
                    options={availableComposers}
                    value={filters.composers}
                    onChange={(e, newValue) => setFilters({ ...filters, composers: newValue })}
                    renderInput={(params) => (
                      <TextField {...params} label="Composers" placeholder="Select composers" />
                    )}
                    renderTags={(value, getTagProps) =>
                      value.map((option, index) => (
                        <Chip variant="outlined" label={option} {...getTagProps({ index })} />
                      ))
                    }
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Autocomplete
                    multiple
                    options={availablePublishers}
                    value={filters.publishers}
                    onChange={(e, newValue) => setFilters({ ...filters, publishers: newValue })}
                    renderInput={(params) => (
                      <TextField {...params} label="Publishers" placeholder="Select publishers" />
                    )}
                    renderTags={(value, getTagProps) =>
                      value.map((option, index) => (
                        <Chip variant="outlined" label={option} {...getTagProps({ index })} />
                      ))
                    }
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Autocomplete
                    multiple
                    options={availableStores}
                    getOptionLabel={(option) => option.name || option}
                    value={filters.storeIds}
                    onChange={(e, newValue) => setFilters({ ...filters, storeIds: newValue })}
                    renderInput={(params) => (
                      <TextField {...params} label="Stores" placeholder="Select stores" />
                    )}
                    renderTags={(value, getTagProps) =>
                      value.map((option, index) => (
                        <Chip variant="outlined" label={option.name || option} {...getTagProps({ index })} />
                      ))
                    }
                  />
                </Grid>
              </Grid>

              <Box sx={{ mt: 2 }}>
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={filters.includeISRC}
                      onChange={(e) => setFilters({ ...filters, includeISRC: e.target.checked })}
                    />
                  }
                  label="Include ISRC Codes"
                />
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={filters.includeISWC}
                      onChange={(e) => setFilters({ ...filters, includeISWC: e.target.checked })}
                    />
                  }
                  label="Include ISWC Codes"
                />
              </Box>
            </AccordionDetails>
          </Accordion>

          <Accordion>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography variant="subtitle1">Location Filters</Typography>
            </AccordionSummary>
            <AccordionDetails>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="City"
                    value={filters.location.city}
                    onChange={(e) => setFilters({
                      ...filters,
                      location: { ...filters.location, city: e.target.value }
                    })}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    select
                    fullWidth
                    label="Province"
                    value={filters.location.province}
                    onChange={(e) => setFilters({
                      ...filters,
                      location: { ...filters.location, province: e.target.value }
                    })}
                  >
                    <MenuItem value="">All Provinces</MenuItem>
                    <MenuItem value="Gauteng">Gauteng</MenuItem>
                    <MenuItem value="Western Cape">Western Cape</MenuItem>
                    <MenuItem value="KwaZulu-Natal">KwaZulu-Natal</MenuItem>
                    <MenuItem value="Eastern Cape">Eastern Cape</MenuItem>
                    <MenuItem value="Free State">Free State</MenuItem>
                    <MenuItem value="Limpopo">Limpopo</MenuItem>
                    <MenuItem value="Mpumalanga">Mpumalanga</MenuItem>
                    <MenuItem value="North West">North West</MenuItem>
                    <MenuItem value="Northern Cape">Northern Cape</MenuItem>
                  </TextField>
                </Grid>
              </Grid>
            </AccordionDetails>
          </Accordion>
        </CardContent>
      </Card>

      {/* Tab Content */}
      {activeTab === 0 && (
        <Card>
          <CardContent>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="h6">
                Compliance Reports ({reports.length})
              </Typography>
              <Stack direction="row" spacing={1}>
                <Button
                  variant="outlined"
                  startIcon={<ExportIcon />}
                  size="small"
                  onClick={() => setExportDialogOpen(true)}
                >
                  Bulk Export
                </Button>
              </Stack>
            </Box>

            {loading ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
                <CircularProgress />
              </Box>
            ) : (
              <>
                <TableContainer
                  component={Paper}
                  variant="outlined"
                  sx={{
                    width: '100%',
                    overflow: 'auto',
                    maxWidth: '100%'
                  }}
                >
                  <Table sx={{ minWidth: 800 }}>
                    <TableHead>
                      <TableRow>
                        <TableCell padding="checkbox">
                          <Checkbox
                            indeterminate={selectedReports.length > 0 && selectedReports.length < reports.length}
                            checked={reports.length > 0 && selectedReports.length === reports.length}
                            onChange={(e) => {
                              if (e.target.checked) {
                                setSelectedReports(reports.map(r => r._id));
                              } else {
                                setSelectedReports([]);
                              }
                            }}
                          />
                        </TableCell>
                        <TableCell sx={{ minWidth: 120 }}>Report ID</TableCell>
                        <TableCell sx={{ minWidth: 100 }}>Type</TableCell>
                        <TableCell sx={{ minWidth: 120 }}>Organization</TableCell>
                        <TableCell sx={{ minWidth: 140 }}>Date Range</TableCell>
                        <TableCell sx={{ minWidth: 80 }}>Tracks</TableCell>
                        <TableCell sx={{ minWidth: 80 }}>Plays</TableCell>
                        <TableCell sx={{ minWidth: 120 }}>Status</TableCell>
                        <TableCell sx={{ minWidth: 120 }}>Created</TableCell>
                        <TableCell sx={{ minWidth: 120 }}>Actions</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {reports.map((report) => (
                        <TableRow key={report._id} hover>
                          <TableCell padding="checkbox">
                            <Checkbox
                              checked={selectedReports.includes(report._id)}
                              onChange={(e) => {
                                if (e.target.checked) {
                                  setSelectedReports([...selectedReports, report._id]);
                                } else {
                                  setSelectedReports(selectedReports.filter(id => id !== report._id));
                                }
                              }}
                            />
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2" sx={{ fontFamily: 'monospace', fontWeight: 600 }}>
                              {report.reportId}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Chip
                              label={report.reportType}
                              size="small"
                              variant="outlined"
                              icon={<ScheduleIcon />}
                            />
                          </TableCell>
                          <TableCell>
                            <Chip
                              label={report.organization}
                              size="small"
                              color={
                                report.organization === 'SAMRO' ? 'primary' :
                                report.organization === 'SAMPRA' ? 'secondary' : 'default'
                              }
                              icon={<BusinessIcon />}
                            />
                          </TableCell>
                          <TableCell>
                            <Box>
                              <Typography variant="body2">
                                {formatDate(report.dateRange.startDate)}
                              </Typography>
                              <Typography variant="caption" color="text.secondary">
                                to {formatDate(report.dateRange.endDate)}
                              </Typography>
                            </Box>
                          </TableCell>
                          <TableCell>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              <MusicIcon fontSize="small" color="action" />
                              <Typography variant="body2">
                                {report.summary?.totalTracks || 0}
                              </Typography>
                            </Box>
                          </TableCell>
                          <TableCell>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              <TrendingUpIcon fontSize="small" color="action" />
                              <Typography variant="body2">
                                {report.summary?.totalPlays || 0}
                              </Typography>
                            </Box>
                          </TableCell>
                          <TableCell>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              {getStatusIcon(report.compliance?.verificationStatus || 'pending')}
                              <Chip
                                label={report.compliance?.verificationStatus || 'pending'}
                                size="small"
                                color={getStatusColor(report.compliance?.verificationStatus || 'pending')}
                              />
                            </Box>
                          </TableCell>
                          <TableCell>
                            <Box>
                              <Typography variant="body2">
                                {formatDate(report.createdAt)}
                              </Typography>
                              <Typography variant="caption" color="text.secondary">
                                {formatDate(report.createdAt, { includeTime: true })}
                              </Typography>
                            </Box>
                          </TableCell>
                          <TableCell>
                            <Stack direction="row" spacing={0.5}>
                              <Tooltip title="View Details">
                                <IconButton
                                  size="small"
                                  onClick={() => {
                                    console.log('View button clicked for report:', report._id);
                                    handleViewReport(report._id);
                                  }}
                                  disabled={viewingReport === report._id}
                                >
                                  {viewingReport === report._id ? (
                                    <CircularProgress size={16} />
                                  ) : (
                                    <ViewIcon />
                                  )}
                                </IconButton>
                              </Tooltip>
                              <Tooltip title="Export CSV">
                                <IconButton
                                  size="small"
                                  onClick={() => {
                                    console.log('CSV export button clicked for report:', report._id);
                                    handleExportReport(report._id, 'csv');
                                  }}
                                  disabled={exportingReports[report._id] === 'csv'}
                                  color={exportingReports[report._id] === 'csv' ? 'primary' : 'default'}
                                >
                                  {exportingReports[report._id] === 'csv' ? (
                                    <CircularProgress size={16} />
                                  ) : (
                                    <DownloadIcon />
                                  )}
                                </IconButton>
                              </Tooltip>
                              <Tooltip title="Export Excel">
                                <IconButton
                                  size="small"
                                  onClick={() => {
                                    console.log('Excel export button clicked for report:', report._id);
                                    handleExportReport(report._id, 'excel');
                                  }}
                                  disabled={exportingReports[report._id] === 'excel'}
                                  color={exportingReports[report._id] === 'excel' ? 'success' : 'success'}
                                >
                                  {exportingReports[report._id] === 'excel' ? (
                                    <CircularProgress size={16} />
                                  ) : (
                                    <ExportIcon />
                                  )}
                                </IconButton>
                              </Tooltip>
                            </Stack>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>

                {/* Pagination */}
                <Box sx={{ display: 'flex', justifyContent: 'center', mt: 3 }}>
                  <Pagination
                    count={totalPages}
                    page={page}
                    onChange={(e, value) => setPage(value)}
                    color="primary"
                    showFirstButton
                    showLastButton
                  />
                </Box>
              </>
            )}
          </CardContent>
        </Card>
      )}






      {/* Aggregated Data Tab */}
      {activeTab === 1 && (
        <Box>
          {/* Aggregated Data Summary */}
          <Grid container spacing={3} sx={{ mb: 3 }}>
            <Grid item xs={12}>
              <Card>
                <CardContent>
                  <Typography variant="h6" sx={{ mb: 2 }}>
                    Top Performing Tracks
                  </Typography>
                  <TableContainer>
                    <Table size="small">
                      <TableHead>
                        <TableRow>
                          <TableCell>Track Title</TableCell>
                          <TableCell>Artist</TableCell>
                          <TableCell>ISRC Code</TableCell>
                          <TableCell>ISWC Code</TableCell>
                          <TableCell align="right">Total Plays</TableCell>
                          <TableCell align="right">Total Duration (hrs)</TableCell>
                          <TableCell>Compliance Status</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {analyticsLoading ? (
                          <TableRow>
                            <TableCell colSpan={7}>
                              <Box sx={{ display: 'flex', justifyContent: 'center', p: 2 }}>
                                <CircularProgress size={24} />
                              </Box>
                            </TableCell>
                          </TableRow>
                        ) : aggregatedData?.topPerformingTracks?.length > 0 ? (
                          aggregatedData.topPerformingTracks.slice(0, 10).map((track, index) => (
                            <TableRow key={index}>
                              <TableCell>{track.title}</TableCell>
                              <TableCell>{track.artist}</TableCell>
                              <TableCell>{track.isrcCode || 'N/A'}</TableCell>
                              <TableCell>{track.iswcCode || 'N/A'}</TableCell>
                              <TableCell align="right">{track.plays}</TableCell>
                              <TableCell align="right">{((track.plays * track.duration) / 3600).toFixed(1)}</TableCell>
                              <TableCell>
                                <Box sx={{ display: 'flex', gap: 0.5 }}>
                                  {track.compliance?.samroRegistered && (
                                    <Chip label="SAMRO" size="small" color="primary" />
                                  )}
                                  {track.compliance?.sampraRegistered && (
                                    <Chip label="SAMPRA" size="small" color="secondary" />
                                  )}
                                  {!track.compliance?.samroRegistered && !track.compliance?.sampraRegistered && (
                                    <Chip label="Pending" size="small" color="warning" />
                                  )}
                                </Box>
                              </TableCell>
                            </TableRow>
                          ))
                        ) : (
                          <TableRow>
                            <TableCell colSpan={7}>
                              <Alert severity="info">
                                No track data available. Generate reports to see detailed track information.
                              </Alert>
                            </TableCell>
                          </TableRow>
                        )}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </CardContent>
              </Card>
            </Grid>
          </Grid>

          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" sx={{ mb: 2 }}>
                    Rights Holder Summary
                  </Typography>
                  <TableContainer>
                    <Table size="small">
                      <TableHead>
                        <TableRow>
                          <TableCell>Rights Holder</TableCell>
                          <TableCell>Type</TableCell>
                          <TableCell align="right">Total Plays</TableCell>
                          <TableCell align="right">Total Duration (hrs)</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {analyticsLoading ? (
                          <TableRow>
                            <TableCell colSpan={4}>
                              <Box sx={{ display: 'flex', justifyContent: 'center', p: 2 }}>
                                <CircularProgress size={24} />
                              </Box>
                            </TableCell>
                          </TableRow>
                        ) : aggregatedData?.rightsHolderSummary?.length > 0 ? (
                          aggregatedData.rightsHolderSummary.slice(0, 8).map((holder, index) => (
                            <TableRow key={index}>
                              <TableCell>{holder.name}</TableCell>
                              <TableCell>
                                <Chip
                                  label={holder.type}
                                  size="small"
                                  color={
                                    holder.type === 'Publisher' ? 'primary' :
                                    holder.type === 'Composer' ? 'secondary' :
                                    holder.type === 'Artist' ? 'success' :
                                    holder.type === 'Rights Holder' ? 'info' :
                                    'default'
                                  }
                                />
                              </TableCell>
                              <TableCell align="right">{holder.totalPlays}</TableCell>
                              <TableCell align="right">{holder.totalDuration.toFixed(1)}</TableCell>
                            </TableRow>
                          ))
                        ) : (
                          <TableRow>
                            <TableCell colSpan={4}>
                              <Alert severity="info">
                                No rights holder data available. Generate reports to see publisher and composer summaries.
                              </Alert>
                            </TableCell>
                          </TableRow>
                        )}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" sx={{ mb: 2 }}>
                    Geographic Distribution
                  </Typography>
                  <TableContainer>
                    <Table size="small">
                      <TableHead>
                        <TableRow>
                          <TableCell>Province</TableCell>
                          <TableCell>City</TableCell>
                          <TableCell align="right">Stores</TableCell>
                          <TableCell align="right">Total Plays</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {analyticsLoading ? (
                          <TableRow>
                            <TableCell colSpan={4}>
                              <Box sx={{ display: 'flex', justifyContent: 'center', p: 2 }}>
                                <CircularProgress size={24} />
                              </Box>
                            </TableCell>
                          </TableRow>
                        ) : aggregatedData?.geographicDistribution?.length > 0 ? (
                          aggregatedData.geographicDistribution.map((location, index) => (
                            <TableRow key={index}>
                              <TableCell>{location.province}</TableCell>
                              <TableCell>{location.city}</TableCell>
                              <TableCell align="right">{location.stores}</TableCell>
                              <TableCell align="right">{location.totalPlays}</TableCell>
                            </TableRow>
                          ))
                        ) : (
                          <TableRow>
                            <TableCell colSpan={4}>
                              <Alert severity="info">
                                No geographic data available. Generate reports to see location-based performance.
                              </Alert>
                            </TableCell>
                          </TableRow>
                        )}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </Box>
      )}

      {/* Enhanced Create Report Dialog */}
      <Dialog open={createDialogOpen} onClose={() => setCreateDialogOpen(false)} maxWidth="lg" fullWidth>
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <ReportIcon />
            Generate Comprehensive Compliance Report
          </Box>
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={3} sx={{ mt: 1 }}>
            {/* Basic Configuration */}
            <Grid item xs={12}>
              <Typography variant="h6" sx={{ mb: 2 }}>Basic Configuration</Typography>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={4}>
                  <TextField
                    select
                    fullWidth
                    label="Report Type"
                    value={newReport.reportType}
                    onChange={(e) => setNewReport({ ...newReport, reportType: e.target.value })}
                  >
                    <MenuItem value="daily">Daily Report</MenuItem>
                    <MenuItem value="weekly">Weekly Report</MenuItem>
                    <MenuItem value="monthly">Monthly Report</MenuItem>
                    <MenuItem value="quarterly">Quarterly Report</MenuItem>
                    <MenuItem value="annual">Annual Report</MenuItem>
                    <MenuItem value="custom">Custom Date Range</MenuItem>
                  </TextField>
                </Grid>
                <Grid item xs={12} sm={4}>
                  <TextField
                    select
                    fullWidth
                    label="Organization"
                    value={newReport.organization || (isSAMRO ? 'SAMRO' : isSAMPRA ? 'SAMPRA' : '')}
                    onChange={(e) => setNewReport({ ...newReport, organization: e.target.value })}
                    disabled={isSAMRO || isSAMPRA} // SAMRO/SAMPRA users cannot change organization
                    helperText={
                      isSAMRO ? 'Reports will be generated for SAMRO only' :
                      isSAMPRA ? 'Reports will be generated for SAMPRA only' :
                      isComplianceAdmin ? 'Select organization or leave empty to auto-detect' :
                      'Organization will be auto-detected from your role'
                    }
                  >
                    {isComplianceAdmin && (
                      <>
                        <MenuItem value="">Auto-detect from user role</MenuItem>
                        <MenuItem value="SAMRO">SAMRO Only</MenuItem>
                        <MenuItem value="SAMPRA">SAMPRA Only</MenuItem>
                        <MenuItem value="All">All Organizations</MenuItem>
                      </>
                    )}
                    {isSAMRO && (
                      <MenuItem value="SAMRO">SAMRO Only</MenuItem>
                    )}
                    {isSAMPRA && (
                      <MenuItem value="SAMPRA">SAMPRA Only</MenuItem>
                    )}
                    {!isComplianceAdmin && !isSAMRO && !isSAMPRA && (
                      <MenuItem value="">Auto-detect from user role</MenuItem>
                    )}
                  </TextField>
                </Grid>
                <Grid item xs={12} sm={4}>
                  <Autocomplete
                    multiple
                    options={availableStores}
                    getOptionLabel={(option) => option.name || option}
                    value={newReport.storeIds}
                    onChange={(e, newValue) => setNewReport({ ...newReport, storeIds: newValue })}
                    renderInput={(params) => (
                      <TextField {...params} label="Select Stores" placeholder="All stores" />
                    )}
                  />
                </Grid>
              </Grid>
            </Grid>

            {/* Date Range */}
            <Grid item xs={12}>
              <Typography variant="h6" sx={{ mb: 2 }}>Date Range</Typography>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <TextField
                    type="date"
                    fullWidth
                    label="Start Date"
                    value={formatDateForInput(newReport.dateRange.startDate)}
                    onChange={(e) => {
                      const dateValue = e.target.value ? new Date(e.target.value) : null;
                      setNewReport({
                        ...newReport,
                        dateRange: { ...newReport.dateRange, startDate: dateValue }
                      });
                    }}
                    InputLabelProps={{ shrink: true }}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    type="date"
                    fullWidth
                    label="End Date"
                    value={formatDateForInput(newReport.dateRange.endDate)}
                    onChange={(e) => {
                      const dateValue = e.target.value ? new Date(e.target.value) : null;
                      setNewReport({
                        ...newReport,
                        dateRange: { ...newReport.dateRange, endDate: dateValue }
                      });
                    }}
                    InputLabelProps={{ shrink: true }}
                  />
                </Grid>
              </Grid>
            </Grid>

            {/* Data Fields to Include */}
            <Grid item xs={12}>
              <Typography variant="h6" sx={{ mb: 2 }}>Data Fields to Include</Typography>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={newReport.includeFields.trackDetails}
                        onChange={(e) => setNewReport({
                          ...newReport,
                          includeFields: { ...newReport.includeFields, trackDetails: e.target.checked }
                        })}
                      />
                    }
                    label="Detailed Track Information (Title, Artist, Album, ISRC, ISWC)"
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={newReport.includeFields.playbackDetails}
                        onChange={(e) => setNewReport({
                          ...newReport,
                          includeFields: { ...newReport.includeFields, playbackDetails: e.target.checked }
                        })}
                      />
                    }
                    label="Playback Details (Timestamps, Duration, Completion %)"
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={newReport.includeFields.locationDetails}
                        onChange={(e) => setNewReport({
                          ...newReport,
                          includeFields: { ...newReport.includeFields, locationDetails: e.target.checked }
                        })}
                      />
                    }
                    label="Location Details (Store Address, City, Province)"
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={newReport.includeFields.rightsHolderInfo}
                        onChange={(e) => setNewReport({
                          ...newReport,
                          includeFields: { ...newReport.includeFields, rightsHolderInfo: e.target.checked }
                        })}
                      />
                    }
                    label="Rights Holder Information (Publishers, Composers, ISRC/ISWC)"
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={newReport.includeFields.complianceInfo}
                        onChange={(e) => setNewReport({
                          ...newReport,
                          includeFields: { ...newReport.includeFields, complianceInfo: e.target.checked }
                        })}
                      />
                    }
                    label="Compliance Information (SAMRO/SAMPRA Registration)"
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={newReport.includeFields.aggregatedData}
                        onChange={(e) => setNewReport({
                          ...newReport,
                          includeFields: { ...newReport.includeFields, aggregatedData: e.target.checked }
                        })}
                      />
                    }
                    label="Aggregated Data (Top Tracks, Play Statistics by Rights Holder)"
                  />
                </Grid>
              </Grid>
            </Grid>

            {/* Export Formats */}
            <Grid item xs={12}>
              <Typography variant="h6" sx={{ mb: 2 }}>Export Formats</Typography>
              <FormControl fullWidth>
                <InputLabel>Select Export Formats</InputLabel>
                <Select
                  multiple
                  value={newReport.exportFormats}
                  onChange={(e) => setNewReport({ ...newReport, exportFormats: e.target.value })}
                  renderValue={(selected) => (
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                      {selected.map((value) => (
                        <Chip key={value} label={value.toUpperCase()} size="small" />
                      ))}
                    </Box>
                  )}
                >
                  <MenuItem value="csv">CSV (Comma Separated Values)</MenuItem>
                  <MenuItem value="excel">Excel (XLSX)</MenuItem>
                  <MenuItem value="pdf">PDF Report</MenuItem>
                  <MenuItem value="xml">XML Data</MenuItem>
                </Select>
              </FormControl>
            </Grid>
          </Grid>

          <Alert severity="info" sx={{ mt: 3 }}>
            <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
              Comprehensive SAMRO/SAMPRA Compliance Report
            </Typography>
            This will generate a detailed compliance report including all essential data required for
            music rights organizations: track metadata, playback logs, rights holder information,
            geographical data, and RISA compliance verification. SAMRO and SAMPRA will perform their own royalty calculations.
          </Alert>

          <Alert severity="warning" sx={{ mt: 1 }}>
            Large date ranges may take several minutes to process. You will be notified when the report is ready.
          </Alert>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setCreateDialogOpen(false)}>Cancel</Button>
          <Button
            onClick={handleCreateReport}
            variant="contained"
            disabled={generating}
            startIcon={generating ? <CircularProgress size={16} /> : <ReportIcon />}
            sx={{
              bgcolor: isSAMRO ? 'primary.main' : isSAMPRA ? 'secondary.main' : 'success.main',
              '&:hover': {
                bgcolor: isSAMRO ? 'primary.dark' : isSAMPRA ? 'secondary.dark' : 'success.dark'
              }
            }}
          >
            {generating ? 'Generating Report...' : 'Generate Compliance Report'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* View Report Dialog */}
      <Dialog open={viewDialogOpen} onClose={() => setViewDialogOpen(false)} maxWidth="lg" fullWidth>
        <DialogTitle>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <ViewIcon />
              Report Details
            </Box>
            {selectedReport && (
              <Chip
                label={selectedReport.reportId}
                color="primary"
                variant="outlined"
                size="small"
              />
            )}
          </Box>
        </DialogTitle>
        <DialogContent>
          {selectedReport ? (
            <Box sx={{ pt: 1 }}>
              {/* Report Summary */}
              <Grid container spacing={3} sx={{ mb: 3 }}>
                <Grid item xs={12} md={6}>
                  <Card variant="outlined">
                    <CardContent>
                      <Typography variant="h6" sx={{ mb: 2 }}>Report Information</Typography>
                      <Stack spacing={1}>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                          <Typography variant="body2" color="text.secondary">Report ID:</Typography>
                          <Typography variant="body2">{selectedReport.reportId}</Typography>
                        </Box>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                          <Typography variant="body2" color="text.secondary">Type:</Typography>
                          <Typography variant="body2">{selectedReport.reportType}</Typography>
                        </Box>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                          <Typography variant="body2" color="text.secondary">Organization:</Typography>
                          <Typography variant="body2">{selectedReport.organization}</Typography>
                        </Box>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                          <Typography variant="body2" color="text.secondary">Status:</Typography>
                          <Chip
                            label={selectedReport.status}
                            size="small"
                            color={selectedReport.status === 'approved' ? 'success' : 'warning'}
                          />
                        </Box>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                          <Typography variant="body2" color="text.secondary">Created:</Typography>
                          <Typography variant="body2">
                            {formatDate(selectedReport.createdAt)}
                          </Typography>
                        </Box>
                      </Stack>
                    </CardContent>
                  </Card>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Card variant="outlined">
                    <CardContent>
                      <Typography variant="h6" sx={{ mb: 2 }}>Summary Statistics</Typography>
                      <Stack spacing={1}>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                          <Typography variant="body2" color="text.secondary">Total Tracks:</Typography>
                          <Typography variant="body2">{selectedReport.summary?.totalTracks || 0}</Typography>
                        </Box>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                          <Typography variant="body2" color="text.secondary">Total Plays:</Typography>
                          <Typography variant="body2">{selectedReport.summary?.totalPlays || 0}</Typography>
                        </Box>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                          <Typography variant="body2" color="text.secondary">Unique Artists:</Typography>
                          <Typography variant="body2">{selectedReport.summary?.uniqueArtists || 0}</Typography>
                        </Box>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                          <Typography variant="body2" color="text.secondary">Duration:</Typography>
                          <Typography variant="body2">
                            {selectedReport.summary?.totalDuration ?
                              `${Math.round(selectedReport.summary.totalDuration / 60)} min` : '0 min'}
                          </Typography>
                        </Box>
                      </Stack>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>

              {/* Date Range */}
              <Card variant="outlined" sx={{ mb: 3 }}>
                <CardContent>
                  <Typography variant="h6" sx={{ mb: 2 }}>Date Range</Typography>
                  <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
                    <Typography variant="body2">
                      <strong>From:</strong> {formatDate(selectedReport.dateRange?.startDate)}
                    </Typography>
                    <Typography variant="body2">→</Typography>
                    <Typography variant="body2">
                      <strong>To:</strong> {formatDate(selectedReport.dateRange?.endDate)}
                    </Typography>
                  </Box>
                </CardContent>
              </Card>

              {/* Stores */}
              {selectedReport.stores && selectedReport.stores.length > 0 && (
                <Card variant="outlined" sx={{ mb: 3 }}>
                  <CardContent>
                    <Typography variant="h6" sx={{ mb: 2 }}>Stores ({selectedReport.stores.length})</Typography>
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                      {selectedReport.stores.map((store, index) => (
                        <Chip
                          key={index}
                          label={store.storeName}
                          variant="outlined"
                          size="small"
                        />
                      ))}
                    </Box>
                  </CardContent>
                </Card>
              )}

              {/* Compliance Status */}
              <Card variant="outlined" sx={{ mb: 3 }}>
                <CardContent>
                  <Typography variant="h6" sx={{ mb: 2 }}>Compliance Status</Typography>
                  <Grid container spacing={2}>
                    <Grid item xs={4}>
                      <Box sx={{ textAlign: 'center' }}>
                        <Typography variant="body2" color="text.secondary">RISA</Typography>
                        <Chip
                          label={selectedReport.compliance?.risaCompliant ? 'Compliant' : 'Non-Compliant'}
                          color={selectedReport.compliance?.risaCompliant ? 'success' : 'error'}
                          size="small"
                        />
                      </Box>
                    </Grid>
                    <Grid item xs={4}>
                      <Box sx={{ textAlign: 'center' }}>
                        <Typography variant="body2" color="text.secondary">SAMRO</Typography>
                        <Chip
                          label={selectedReport.compliance?.samroCompliant ? 'Compliant' : 'Non-Compliant'}
                          color={selectedReport.compliance?.samroCompliant ? 'success' : 'error'}
                          size="small"
                        />
                      </Box>
                    </Grid>
                    <Grid item xs={4}>
                      <Box sx={{ textAlign: 'center' }}>
                        <Typography variant="body2" color="text.secondary">SAMPRA</Typography>
                        <Chip
                          label={selectedReport.compliance?.sampraCompliant ? 'Compliant' : 'Non-Compliant'}
                          color={selectedReport.compliance?.sampraCompliant ? 'success' : 'error'}
                          size="small"
                        />
                      </Box>
                    </Grid>
                  </Grid>
                  {selectedReport.compliance?.issues && selectedReport.compliance.issues.length > 0 && (
                    <Alert severity="warning" sx={{ mt: 2 }}>
                      <Typography variant="body2">
                        {selectedReport.compliance.issues.length} compliance issue(s) found
                      </Typography>
                    </Alert>
                  )}
                </CardContent>
              </Card>

              {/* Track Data Preview */}
              {selectedReport.trackData && selectedReport.trackData.length > 0 && (
                <Card variant="outlined">
                  <CardContent>
                    <Typography variant="h6" sx={{ mb: 2 }}>
                      Track Data Preview ({selectedReport.trackData.length} tracks)
                    </Typography>
                    <TableContainer component={Paper} variant="outlined" sx={{ maxHeight: 300 }}>
                      <Table size="small" stickyHeader>
                        <TableHead>
                          <TableRow>
                            <TableCell>Title</TableCell>
                            <TableCell>Artist</TableCell>
                            <TableCell>Plays</TableCell>
                            <TableCell>Duration</TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {selectedReport.trackData.slice(0, 10).map((track, index) => (
                            <TableRow key={index}>
                              <TableCell>{track.title || 'N/A'}</TableCell>
                              <TableCell>{track.artist || 'N/A'}</TableCell>
                              <TableCell>{track.playCount || 0}</TableCell>
                              <TableCell>{track.duration ? `${Math.round(track.duration / 60)}:${String(track.duration % 60).padStart(2, '0')}` : 'N/A'}</TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </TableContainer>
                    {selectedReport.trackData.length > 10 && (
                      <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
                        Showing first 10 of {selectedReport.trackData.length} tracks
                      </Typography>
                    )}
                  </CardContent>
                </Card>
              )}
            </Box>
          ) : (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
              <CircularProgress />
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setViewDialogOpen(false)}>Close</Button>
          {selectedReport && (
            <Button
              variant="contained"
              startIcon={<DownloadIcon />}
              onClick={() => handleExportReport(selectedReport._id, 'csv')}
            >
              Export CSV
            </Button>
          )}
        </DialogActions>
      </Dialog>

      {/* Bulk Export Dialog */}
      <Dialog open={exportDialogOpen} onClose={() => setExportDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Bulk Export Reports</DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 1 }}>
            <Typography variant="body1" sx={{ mb: 2 }}>
              Export {selectedReports.length} selected report{selectedReports.length !== 1 ? 's' : ''} in multiple formats.
            </Typography>

            <Typography variant="h6" sx={{ mb: 1 }}>Export Formats</Typography>
            <FormControl fullWidth sx={{ mb: 3 }}>
              <InputLabel>Select Export Formats</InputLabel>
              <Select
                multiple
                value={bulkExportFormats}
                onChange={(e) => setBulkExportFormats(e.target.value)}
                renderValue={(selected) => (
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                    {selected.map((value) => (
                      <Chip key={value} label={value.toUpperCase()} size="small" />
                    ))}
                  </Box>
                )}
              >
                <MenuItem value="csv">CSV (Comma Separated Values)</MenuItem>
                <MenuItem value="excel">Excel (XLSX)</MenuItem>
                <MenuItem value="pdf">PDF Report</MenuItem>
                <MenuItem value="xml">XML Data</MenuItem>
              </Select>
            </FormControl>

            <Alert severity="info" sx={{ mb: 2 }}>
              <Typography variant="body2">
                Each selected report will be exported in all selected formats.
                Large exports may take several minutes to complete.
              </Typography>
            </Alert>

            {selectedReports.length > 10 && (
              <Alert severity="warning">
                <Typography variant="body2">
                  You have selected {selectedReports.length} reports. This may take a while to process.
                </Typography>
              </Alert>
            )}
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setExportDialogOpen(false)}>Cancel</Button>
          <Button
            onClick={handleBulkExport}
            variant="contained"
            disabled={bulkExporting || selectedReports.length === 0 || bulkExportFormats.length === 0}
            startIcon={bulkExporting ? <CircularProgress size={16} /> : <ExportIcon />}
          >
            {bulkExporting ? 'Exporting...' : `Export ${selectedReports.length} Report${selectedReports.length !== 1 ? 's' : ''}`}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ComplianceReports;
