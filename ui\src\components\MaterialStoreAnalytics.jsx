import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Grid,
  Paper,
  Card,
  CardContent,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  LinearProgress,
  Alert,
  useTheme
} from '@mui/material';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend
} from 'chart.js';
import { Line, Bar, Doughnut } from 'react-chartjs-2';
import { useAuth } from '../context/AuthContext';
import { storeService, historyService } from '../services/api';
import MaterialMetricCard from './MaterialMetricCard';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import MusicNoteIcon from '@mui/icons-material/MusicNote';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import PeopleIcon from '@mui/icons-material/People';
import BarChartIcon from '@mui/icons-material/BarChart';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend
);

const MaterialStoreAnalytics = () => {
  const { user } = useAuth();
  const theme = useTheme();
  const [loading, setLoading] = useState(true);
  const [timeRange, setTimeRange] = useState('week');
  const [analyticsData, setAnalyticsData] = useState({
    totalPlays: 0,
    uniqueTracks: 0,
    avgSessionTime: 0,
    peakHour: 0,
    topTracks: [],
    playsByHour: [],
    genreDistribution: [],
    recentActivity: []
  });

  useEffect(() => {
    loadAnalyticsData();
  }, [timeRange, user?.storeId]);

  const loadAnalyticsData = async () => {
    if (!user?.storeId) return;
    
    setLoading(true);
    try {
      // Load store-specific analytics data
      const [analyticsRes, topTracksRes, historyRes] = await Promise.all([
        storeService.getAnalytics(user.storeId, timeRange),
        storeService.getTopTracks(user.storeId, 10),
        historyService.get({
          limit: 20,
          storeId: user.storeId,
          sortBy: 'playedDate',
          sortOrder: 'desc'
        })
      ]);

      // Handle new API response structure
      const analyticsData = analyticsRes.data?.success ? analyticsRes.data.data : analyticsRes.data;
      const topTracksData = topTracksRes.data?.success ? topTracksRes.data.data : topTracksRes.data;
      const historyData = historyRes.data?.success ? historyRes.data.data : historyRes.data;

      // Process the analytics data
      const analytics = analyticsData || {};
      const topTracks = Array.isArray(topTracksData) ? topTracksData : [];
      const recentActivity = Array.isArray(historyData) ? historyData : [];

      // Use real data from analytics
      const basicStats = analytics.basic || {};
      const playsByHour = analytics.hourlyDistribution || [];
      const genreDistribution = analytics.genreDistribution || [];

      // Calculate peak hour from hourly data
      const peakHour = playsByHour.length > 0
        ? playsByHour.reduce((max, current) => current.plays > max.plays ? current : max, playsByHour[0])._id || 0
        : 0;

      // Calculate average session time from completion rates
      const avgCompletionRate = basicStats.avgCompletionRate || 0;
      const avgSessionTime = avgCompletionRate > 0 ? (avgCompletionRate / 100) * 4 : 0; // Estimate based on completion rate

      setAnalyticsData({
        totalPlays: basicStats.totalPlays || 0,
        uniqueTracks: basicStats.uniqueTracks || 0,
        avgSessionTime: Math.round(avgSessionTime * 10) / 10, // Round to 1 decimal
        peakHour: peakHour,
        topTracks: topTracks.slice(0, 10),
        playsByHour: playsByHour.map(item => ({
          hour: item._id,
          plays: item.plays
        })),
        genreDistribution: genreDistribution.map(item => ({
          genre: item._id || 'Unknown',
          count: item.plays,
          percentage: 0 // Will be calculated if needed
        })),
        recentActivity: recentActivity.slice(0, 10)
      });
    } catch (error) {
      console.error('Failed to load store analytics:', error);
      // Set empty data instead of fallback
      setAnalyticsData({
        totalPlays: 0,
        uniqueTracks: 0,
        avgSessionTime: 0,
        peakHour: 0,
        topTracks: [],
        playsByHour: [],
        genreDistribution: [],
        recentActivity: []
      });
    } finally {
      setLoading(false);
    }
  };

  // Chart configurations
  const hourlyPlaysChart = {
    labels: analyticsData.playsByHour.map(item => `${item.hour}:00`),
    datasets: [
      {
        label: 'Plays',
        data: analyticsData.playsByHour.map(item => item.plays),
        borderColor: theme.palette.primary.main,
        backgroundColor: theme.palette.primary.light,
        tension: 0.4,
        fill: true
      }
    ]
  };

  const genreChart = {
    labels: analyticsData.genreDistribution.map(item => item.genre),
    datasets: [
      {
        data: analyticsData.genreDistribution.map(item => item.count),
        backgroundColor: [
          theme.palette.primary.main,
          theme.palette.secondary.main,
          theme.palette.success.main,
          theme.palette.warning.main,
          theme.palette.error.main
        ]
      }
    ]
  };

  const statsData = [
    {
      title: 'Total Plays',
      value: analyticsData.totalPlays.toString(),
      icon: <MusicNoteIcon sx={{ fontSize: 32 }} />,
      color: 'primary',
      trend: '+12%'
    },
    {
      title: 'Unique Tracks',
      value: analyticsData.uniqueTracks.toString(),
      icon: <BarChartIcon sx={{ fontSize: 32 }} />,
      color: 'success',
      trend: '+5%'
    },
    {
      title: 'Avg Session',
      value: `${analyticsData.avgSessionTime}h`,
      icon: <AccessTimeIcon sx={{ fontSize: 32 }} />,
      color: 'secondary',
      trend: '+8%'
    },
    {
      title: 'Peak Hour',
      value: `${analyticsData.peakHour}:00`,
      icon: <TrendingUpIcon sx={{ fontSize: 32 }} />,
      color: 'warning',
      trend: 'Stable'
    }
  ];

  if (loading) {
    return (
      <Box sx={{ p: 3 }}>
        <Typography variant="h4" sx={{ mb: 3, fontWeight: 600 }}>
          Store Analytics
        </Typography>
        <LinearProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Typography variant="h4" sx={{ fontWeight: 600 }}>
          Store Analytics
        </Typography>
        <FormControl sx={{ minWidth: 120 }}>
          <InputLabel>Time Range</InputLabel>
          <Select
            value={timeRange}
            label="Time Range"
            onChange={(e) => setTimeRange(e.target.value)}
          >
            <MenuItem value="day">Today</MenuItem>
            <MenuItem value="week">This Week</MenuItem>
            <MenuItem value="month">This Month</MenuItem>
            <MenuItem value="quarter">This Quarter</MenuItem>
          </Select>
        </FormControl>
      </Box>

      {/* Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        {statsData.map((stat, index) => (
          <Grid item xs={12} sm={6} md={3} key={index}>
            <MaterialMetricCard {...stat} />
          </Grid>
        ))}
      </Grid>

      {/* Charts */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        {/* Hourly Plays */}
        <Grid item xs={12} lg={8}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" sx={{ mb: 3 }}>
              Plays by Hour
            </Typography>
            <Box sx={{ height: 300 }}>
              <Line 
                data={hourlyPlaysChart} 
                options={{ 
                  responsive: true, 
                  maintainAspectRatio: false,
                  plugins: {
                    legend: {
                      display: false
                    }
                  }
                }} 
              />
            </Box>
          </Paper>
        </Grid>

        {/* Genre Distribution */}
        <Grid item xs={12} lg={4}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" sx={{ mb: 3 }}>
              Genre Distribution
            </Typography>
            <Box sx={{ height: 300 }}>
              <Doughnut 
                data={genreChart} 
                options={{ 
                  responsive: true, 
                  maintainAspectRatio: false,
                  plugins: {
                    legend: {
                      position: 'bottom'
                    }
                  }
                }} 
              />
            </Box>
          </Paper>
        </Grid>
      </Grid>

      {/* Top Tracks and Recent Activity */}
      <Grid container spacing={3}>
        {/* Top Tracks */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" sx={{ mb: 3 }}>
              Top Tracks
            </Typography>
            {analyticsData.topTracks.length > 0 ? (
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Track</TableCell>
                      <TableCell align="right">Plays</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {analyticsData.topTracks.map((track, index) => (
                      <TableRow key={index}>
                        <TableCell>
                          <Box>
                            <Typography variant="body2" sx={{ fontWeight: 500 }}>
                              {track.title || `Track ${index + 1}`}
                            </Typography>
                            <Typography variant="caption" sx={{ color: 'text.secondary' }}>
                              {track.artist || 'Unknown Artist'}
                            </Typography>
                          </Box>
                        </TableCell>
                        <TableCell align="right">
                          <Chip
                            label={track.plays || track.playCount || 0}
                            size="small"
                            color="primary"
                          />
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            ) : (
              <Alert severity="info">No track data available for this period</Alert>
            )}
          </Paper>
        </Grid>

        {/* Recent Activity */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" sx={{ mb: 3 }}>
              Recent Activity
            </Typography>
            {analyticsData.recentActivity.length > 0 ? (
              <Box sx={{ maxHeight: 400, overflow: 'auto' }}>
                {analyticsData.recentActivity.map((activity, index) => (
                  <Box key={index} sx={{ mb: 2, p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
                    <Typography variant="body2" sx={{ fontWeight: 500, color: 'black' }}>
                      {activity.trackId?.title || 'Unknown Track'}
                    </Typography>
                    <Typography variant="caption" sx={{ color: 'text.secondary' }}>
                      {new Date(activity.playedDate).toLocaleString()}
                    </Typography>
                  </Box>
                ))}
              </Box>
            ) : (
              <Alert severity="info">No recent activity data available</Alert>
            )}
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default MaterialStoreAnalytics;
