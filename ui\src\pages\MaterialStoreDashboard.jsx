import { useState, useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import { useMedia } from '../context/MediaContext';
import { playlistService, trackService, historyService, storeService } from '../services/api';
import { audioService } from '../services/audioService';
import { radioService } from '../services/radioService';
import { scheduleService } from '../services/scheduleService';
import activityLogger from '../services/activityLogger';
import offlineService from '../services/offlineService';
import { validateTracks, validatePlaylists, validateApiResponse } from '../utils/dataValidation';

// Legacy validation function for history (to be moved to utils later)
const validateHistory = (history) => {
  if (!Array.isArray(history)) return [];

  return history.filter(item =>
    item &&
    item.trackId &&
    item.storeId &&
    item.startTime
  );
};
import {
  Box,
  Typography,
  Grid,
  Paper,
  Button,
  IconButton,
  AppBar,
  Toolbar,
  useMediaQuery,
  useTheme,
  LinearProgress,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Alert,
  Chip,
  Slider,
  Pagination,
  Stack,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  ListItemButton
} from '@mui/material';
import MusicNoteIcon from '@mui/icons-material/MusicNote';
import VolumeUpIcon from '@mui/icons-material/VolumeUp';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import PauseIcon from '@mui/icons-material/Pause';
import SkipPreviousIcon from '@mui/icons-material/SkipPrevious';
import SkipNextIcon from '@mui/icons-material/SkipNext';
import ShuffleIcon from '@mui/icons-material/Shuffle';
import RepeatIcon from '@mui/icons-material/Repeat';
import MenuIcon from '@mui/icons-material/Menu';
import CircleIcon from '@mui/icons-material/Circle';
import CalendarTodayIcon from '@mui/icons-material/CalendarToday';
import PeopleIcon from '@mui/icons-material/People';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import QueueMusicIcon from '@mui/icons-material/QueueMusic';
import CloseIcon from '@mui/icons-material/Close';
import MaterialSidebar from '../components/MaterialSidebar';
import MaterialMetricCard from '../components/MaterialMetricCard';
import ScheduledPlaylistIndicator from '../components/ScheduledPlaylistIndicator';

const MaterialStoreDashboard = () => {
  const { user } = useAuth();
  const { playMusic } = useMedia();
  const [searchParams] = useSearchParams();
  const [activeTab, setActiveTab] = useState(searchParams.get('tab') || 'player');
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [loading, setLoading] = useState(true);
  const [playlists, setPlaylists] = useState([]);
  const [tracks, setTracks] = useState([]);
  const [recentActivity, setRecentActivity] = useState([]);
  const [currentTrack, setCurrentTrack] = useState(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [volume, setVolume] = useState(audioService.getVolumePercentage());
  const [progress, setProgress] = useState(0);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [activityPage, setActivityPage] = useState(0);
  const [activityPageSize] = useState(5);
  const [store, setStore] = useState(null);
  const [playlistDialogOpen, setPlaylistDialogOpen] = useState(false);
  const [currentPlaylist, setCurrentPlaylist] = useState(null);
  const [dataQuality, setDataQuality] = useState({
    tracks: { valid: 0, invalid: 0, total: 0 },
    playlists: { valid: 0, invalid: 0, total: 0 },
    lastSync: null,
    errors: []
  });
  const [showDataQualityAlert, setShowDataQualityAlert] = useState(false);
  const [storeAnalytics, setStoreAnalytics] = useState({
    totalPlays: 0,
    totalDuration: 0,
    uniqueTracks: 0
  });
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('lg'));

  useEffect(() => {
    const tab = searchParams.get('tab');
    if (tab) {
      setActiveTab(tab);
    }
  }, [searchParams]);

  useEffect(() => {
    loadDashboardData();

    // Subscribe to audio service events
    const unsubscribeAudio = audioService.subscribe((state) => {
      setCurrentTrack(state.currentTrack);
      setIsPlaying(state.isPlaying);
      setVolume(audioService.getVolumePercentage());
      setProgress(state.progress || 0);
      setCurrentTime(state.currentTime || 0);
      setDuration(state.duration || 0);

      // Refresh dashboard data when a track finishes playing to update recent activity
      if (state.trackFinished) {
        setTimeout(() => {
          loadDashboardData();
        }, 1000); // Small delay to ensure the play log is processed
      }
    });

    // Subscribe to offline service events
    const unsubscribeOffline = offlineService.subscribe((online) => {
      setIsOnline(online);
      if (online) {
        // Reload data when back online
        loadDashboardData();
      }
    });

    // Prepare offline mode with current data
    const storeId = user?.storeId;
    if (storeId && navigator.onLine) {
      offlineService.prepareOfflineMode(storeId);
      // Initialize activity logger with store ID
      activityLogger.setStoreId(storeId);
      radioService.setStoreId(storeId);
      // CRITICAL FIX: Set store ID for audio service to enable play logging
      audioService.setStoreId(storeId);

      // Initialize and start schedule service
      scheduleService.setStoreId(storeId);
      scheduleService.start();
    }

    return () => {
      unsubscribeAudio();
      unsubscribeOffline();
      scheduleService.stop();
    };
  }, [user?.storeId]);

  const loadDashboardData = async () => {
    setLoading(true);
    try {
      // Get store ID from user context
      const storeId = user?.storeId;
      if (!storeId) {
        console.error('No store ID found for user');
        return;
      }

      if (isOnline) {
        // Load from API when online
        const [playlistsRes, tracksRes, historyRes, storeRes, analyticsRes] = await Promise.all([
          playlistService.getByStore(storeId),
          trackService.getAll(),
          historyService.get({
            limit: 10,
            storeId,
            sortBy: 'playedDate',
            sortOrder: 'desc'
          }),
          storeService.getById(storeId),
          storeService.getAnalytics(storeId, 'week').catch(err => {
            console.warn('Failed to load store analytics:', err);
            return { data: { basic: { totalPlays: 0, totalDuration: 0, uniqueTracks: 0 } } };
          })
        ]);

        // Validate API responses first
        const playlistsValidation = validateApiResponse(playlistsRes.data);
        const tracksValidation = validateApiResponse(tracksRes.data);
        const historyValidation = validateApiResponse(historyRes.data);
        const storeValidation = validateApiResponse(storeRes.data);
        const analyticsValidation = validateApiResponse(analyticsRes.data);

        // Extract data from validated responses
        const playlistsData = playlistsValidation.data;
        const tracksData = tracksValidation.data;
        const historyData = historyValidation.data;
        const storeData = storeValidation.data;
        const analyticsData = analyticsValidation.data;

        // Validate and filter data using comprehensive validation
        const playlistValidationResult = validatePlaylists(playlistsData);
        const trackValidationResult = validateTracks(tracksData);
        const validatedHistory = validateHistory(historyData);

        const validatedPlaylists = playlistValidationResult.validPlaylists;
        const validatedTracks = trackValidationResult.validTracks;

        setPlaylists(validatedPlaylists);
        setTracks(validatedTracks);
        setRecentActivity(validatedHistory);
        setStore(storeData || null);

        // Set analytics data for better statistics
        if (analyticsData && analyticsData.basic) {
          console.log('Store analytics loaded:', analyticsData.basic);
          setStoreAnalytics({
            totalPlays: analyticsData.basic.totalPlays || 0,
            totalDuration: analyticsData.basic.totalDuration || 0,
            uniqueTracks: analyticsData.basic.uniqueTracks || 0
          });
        } else {
          console.log('No analytics data available, using defaults');
        }

        // Update data quality metrics using validation results
        const allErrors = [
          ...(!playlistsValidation.isValid ? playlistsValidation.errors : []),
          ...(!tracksValidation.isValid ? tracksValidation.errors : []),
          ...(!historyValidation.isValid ? historyValidation.errors : []),
          ...(!storeValidation.isValid ? storeValidation.errors : []),
          ...(!analyticsValidation.isValid ? analyticsValidation.errors : [])
        ];

        setDataQuality({
          tracks: trackValidationResult.summary,
          playlists: playlistValidationResult.summary,
          lastSync: new Date().toISOString(),
          errors: allErrors
        });

        // Show alert if there are data quality issues
        const hasIssues = trackValidationResult.summary.invalid > 0 ||
                          playlistValidationResult.summary.invalid > 0 ||
                          allErrors.length > 0;
        setShowDataQualityAlert(hasIssues);

        console.log('Data Quality Report:', {
          playlists: playlistValidationResult.summary,
          tracks: trackValidationResult.summary,
          apiErrors: allErrors,
          timestamp: new Date().toISOString()
        });

        // Store data offline for future use
        if (Array.isArray(playlistsData)) {
          offlineService.storePlaylistOffline(playlistsData);
        }
        if (Array.isArray(tracksData)) {
          offlineService.storeTracksOffline(tracksData);
        }
      } else {
        // Load from offline storage when offline
        const offlinePlaylists = offlineService.getOfflinePlaylist() || [];
        const offlineTracks = offlineService.getOfflineTracks() || [];

        setPlaylists(Array.isArray(offlinePlaylists) ? offlinePlaylists : []);
        setTracks(Array.isArray(offlineTracks) ? offlineTracks : []);
        setRecentActivity([]); // No recent activity when offline

        // Try to get store info from localStorage
        const cachedStore = localStorage.getItem(`store_${storeId}`);
        if (cachedStore) {
          try {
            setStore(JSON.parse(cachedStore));
          } catch (e) {
            console.warn('Failed to parse cached store data');
          }
        }
      }
    } catch (error) {
      console.error('Failed to load dashboard data:', error);

      // Fallback to offline data if API fails and we're online
      if (isOnline) {
        const offlinePlaylists = offlineService.getOfflinePlaylist() || [];
        const offlineTracks = offlineService.getOfflineTracks() || [];

        setPlaylists(Array.isArray(offlinePlaylists) ? offlinePlaylists : []);
        setTracks(Array.isArray(offlineTracks) ? offlineTracks : []);
        setRecentActivity([]);

        // Try to get cached store data
        const cachedStore = localStorage.getItem(`store_${storeId}`);
        if (cachedStore) {
          try {
            setStore(JSON.parse(cachedStore));
          } catch (e) {
            console.warn('Failed to parse cached store data');
            setStore(null);
          }
        } else {
          setStore(null);
        }
      } else {
        // If offline and error occurred, set empty states
        setPlaylists([]);
        setTracks([]);
        setRecentActivity([]);
        setStore(null);
      }
    } finally {
      setLoading(false);
    }
  };

  // Audio control functions
  const handlePlayPause = () => {
    if (isPlaying) {
      audioService.pause();
    } else {
      if (audioService.getCurrentTrack()) {
        audioService.resume();
      } else if (tracks.length > 0) {
        // Use MediaContext to ensure proper coordination with radio
        playMusic(tracks, 0, { name: 'Dashboard Tracks' });
      }
    }
  };

  const handlePrevious = () => {
    audioService.previous();
  };

  const handleNext = () => {
    audioService.next();
  };

  const handleShuffle = () => {
    audioService.toggleShuffle();
  };

  const handleRepeat = () => {
    audioService.toggleRepeat();
  };

  const handleVolumeChange = (_, newValue) => {
    const volumeValue = newValue / 100; // Convert percentage to 0-1 range
    audioService.setVolume(volumeValue);
  };

  const handleActivityPageChange = (_, newPage) => {
    setActivityPage(newPage - 1); // MUI Pagination is 1-based, our state is 0-based
  };

  const handleShowPlaylist = () => {
    // Get current playlist from audio service
    const playlist = audioService.getPlaylist();
    if (playlist && playlist.length > 0) {
      setCurrentPlaylist(playlist);
      setPlaylistDialogOpen(true);
    }
  };

  const handlePlayTrack = (index) => {
    // Use MediaContext to ensure proper coordination
    const currentPlaylist = audioService.getPlaylist();
    if (currentPlaylist && currentPlaylist.length > 0) {
      playMusic(currentPlaylist, index, { name: audioService.getCurrentPlaylistName() || 'Current Playlist' });
    }
    setPlaylistDialogOpen(false);
  };

  const handleViewAllSchedule = () => {
    // Navigate to schedule page
    window.location.href = '/store?tab=schedule';
  };

  // Calculate stats from real data
  // Use unique tracks from analytics (tracks actually played at this store) or fallback to total tracks
  const totalTracks = storeAnalytics.uniqueTracks > 0 ? storeAnalytics.uniqueTracks : tracks.length;
  const activePlaylists = playlists.filter(p => p.isActive !== false).length;
  // Use analytics data for more accurate statistics
  const totalPlays = storeAnalytics.totalPlays || recentActivity.length;
  const hoursPlayed = storeAnalytics.totalDuration ? Math.floor(storeAnalytics.totalDuration / 3600) : Math.floor(totalPlays * 3.5 / 60); // Convert seconds to hours or estimate

  const statsData = [
    {
      title: 'Total Tracks',
      value: totalTracks.toString(),
      icon: <MusicNoteIcon sx={{ fontSize: 32 }} />,
      color: 'primary'
    },
    {
      title: 'Active Playlists',
      value: activePlaylists.toString(),
      icon: <TrendingUpIcon sx={{ fontSize: 32 }} />,
      color: 'success'
    },
    {
      title: 'Hours Played',
      value: hoursPlayed.toString(),
      icon: <AccessTimeIcon sx={{ fontSize: 32 }} />,
      color: 'secondary'
    },
    {
      title: 'Recent Plays',
      value: totalPlays.toString(),
      icon: <PeopleIcon sx={{ fontSize: 32 }} />,
      color: 'warning'
    }
  ];

  // Format recent activity from API data
  const formattedActivity = recentActivity.map(activity => {
    // Handle different activity data structures
    const trackTitle = activity.trackId?.title || activity.trackName || 'Unknown Track';
    const trackArtist = activity.trackId?.artist || activity.trackArtist || 'Unknown Artist';
    const storeName = activity.storeId?.name || 'Store';
    const timestamp = activity.timestamp || activity.startTime || activity.endTime;
    const duration = activity.durationPlayed ? `${Math.floor(activity.durationPlayed / 60)}:${(activity.durationPlayed % 60).toString().padStart(2, '0')}` : '';

    return {
      action: 'Track played',
      detail: `${trackTitle} by ${trackArtist}`,
      store: storeName,
      duration: duration,
      time: timestamp ? new Date(timestamp).toLocaleTimeString() : 'Unknown',
      fullTimestamp: timestamp ? new Date(timestamp) : new Date()
    };
  }).sort((a, b) => b.fullTimestamp - a.fullTimestamp); // Sort by most recent first

  // Paginate activity
  const paginatedActivity = formattedActivity.slice(
    activityPage * activityPageSize,
    (activityPage + 1) * activityPageSize
  );
  const totalActivityPages = Math.ceil(formattedActivity.length / activityPageSize);

  // Get upcoming schedule from playlists
  const getUpcomingSchedule = () => {
    if (!Array.isArray(playlists) || playlists.length === 0) return [];

    const now = new Date();
    const currentDay = now.toLocaleString('en-US', { weekday: 'long' });
    const currentTime = now.toTimeString().slice(0, 5);

    const upcomingSchedules = [];

    playlists.forEach(playlist => {
      if (playlist.schedule && Array.isArray(playlist.schedule)) {
        playlist.schedule.forEach(schedule => {
          // Check if this schedule is for today and in the future
          if (schedule.day === currentDay && schedule.startTime > currentTime) {
            upcomingSchedules.push({
              name: playlist.name,
              duration: calculateDuration(schedule.startTime, schedule.endTime),
              time: formatTime(schedule.startTime),
              startTime: schedule.startTime
            });
          }
        });
      }
    });

    // Sort by start time and return first 3
    return upcomingSchedules
      .sort((a, b) => a.startTime.localeCompare(b.startTime))
      .slice(0, 3);
  };

  const calculateDuration = (startTime, endTime) => {
    const start = new Date(`2000-01-01T${startTime}`);
    const end = new Date(`2000-01-01T${endTime}`);
    const diffMs = end - start;
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));

    if (diffHours > 0) {
      return diffMinutes > 0 ? `${diffHours}h ${diffMinutes}m` : `${diffHours} hour${diffHours > 1 ? 's' : ''}`;
    }
    return `${diffMinutes} minutes`;
  };

  const formatTime = (time) => {
    if (!time) return '';
    const [hours, minutes] = time.split(':');
    const hour = parseInt(hours);
    const ampm = hour >= 12 ? 'PM' : 'AM';
    const displayHour = hour % 12 || 12;
    return `${displayHour}:${minutes} ${ampm}`;
  };

  const upcomingSchedule = getUpcomingSchedule();

  return (
    <Box sx={{ display: 'flex', minHeight: '100vh' }}>
      {/* Sidebar */}
      <MaterialSidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />

      {/* Main Content */}
      <Box sx={{ flexGrow: 1, display: 'flex', flexDirection: 'column' }}>
        {/* Mobile Header */}
        {isMobile && (
          <AppBar position="static" sx={{ bgcolor: 'background.paper' }}>
            <Toolbar>
              <IconButton
                edge="start"
                color="inherit"
                onClick={() => setSidebarOpen(true)}
                sx={{ mr: 2 }}
              >
                <MenuIcon />
              </IconButton>
              <Typography variant="h6" sx={{ flexGrow: 1 }}>
                Store Dashboard
              </Typography>
            </Toolbar>
          </AppBar>
        )}

        {/* Content Area */}
        <Box sx={{ p: 3, flexGrow: 1 }}>
          {/* Offline Alert */}
          {!isOnline && (
            <Alert severity="warning" sx={{ mb: 3 }}>
              You are currently offline. Some features may be limited. Data will sync when connection is restored.
            </Alert>
          )}

         

          {/* Header Section */}
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
            <Box>
              <Typography variant="h4" sx={{ fontWeight: 700, mb: 1 }}>
                {store?.name || 'Music Player'}
              </Typography>
              
            </Box>

            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              {/* Offline Indicator */}
              {!isOnline && (
                <Chip
                  label="Offline Mode"
                  color="warning"
                  size="small"
                  sx={{ mr: 2 }}
                />
              )}

              <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                Store Volume
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <VolumeUpIcon sx={{ color: 'text.secondary', fontSize: 16 }} />
                <Slider
                  value={volume}
                  onChange={handleVolumeChange}
                  min={0}
                  max={100}
                  sx={{
                    width: 80,
                    color: 'success.main',
                    '& .MuiSlider-thumb': {
                      width: 12,
                      height: 12
                    },
                    '& .MuiSlider-track': {
                      height: 4
                    },
                    '& .MuiSlider-rail': {
                      height: 4
                    }
                  }}
                />
                <Typography variant="body2" sx={{ color: 'text.secondary', minWidth: '35px' }}>
                  {volume}%
                </Typography>
              </Box>
            </Box>
          </Box>

          {/* Stats Cards */}
          <Grid container spacing={3} sx={{ mb: 4 }}>
            {loading ? (
              Array.from({ length: 4 }).map((_, index) => (
                <Grid item xs={12} sm={6} md={3} key={index}>
                  <MaterialMetricCard
                    title="Loading..."
                    value="--"
                    icon={<CircleIcon sx={{ fontSize: 32 }} />}
                    color="primary"
                  />
                </Grid>
              ))
            ) : (
              statsData.map((stat, index) => (
                <Grid item xs={12} sm={6} md={3} key={index}>
                  <MaterialMetricCard {...stat} />
                </Grid>
              ))
            )}
          </Grid>

          {/* Now Playing Section */}
          <Paper sx={{ p: 3, mb: 4, bgcolor: 'background.paper', borderRadius: 3 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
              <Typography variant="h6" sx={{ fontWeight: 600 }}>
                Now Playing
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <IconButton size="small" onClick={handleShowPlaylist} title="Show Current Playlist">
                  <QueueMusicIcon />
                </IconButton>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <CircleIcon sx={{ fontSize: 8, color: 'success.main', animation: 'pulse 2s infinite' }} />
                  <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                    Live
                  </Typography>
                </Box>
              </Box>
            </Box>

            <Box sx={{ display: 'flex', alignItems: 'center', gap: 3 }}>
              {/* Album Art */}
              <Box
                sx={{
                  width: 80,
                  height: 80,
                  background: 'linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%)',
                  borderRadius: 2,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}
              >
                <MusicNoteIcon sx={{ color: 'white', fontSize: 32 }} />
              </Box>

              {/* Track Info */}
              <Box sx={{ flex: 1 }}>
                <Typography variant="h5" sx={{ fontWeight: 600, mb: 0.5 }}>
                  {currentTrack?.title || 'No track playing'}
                </Typography>
                <Typography variant="body2" sx={{ color: 'text.secondary', mb: 2 }}>
                  {currentTrack?.artist || 'Unknown Artist'} • {currentTrack?.album || 'Unknown Album'}
                </Typography>

                {/* Progress Bar */}
                <Box>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                    <Typography variant="caption" sx={{ color: 'text.secondary' }}>
                      {audioService.formatTime(currentTime)}
                    </Typography>
                    <Typography variant="caption" sx={{ color: 'text.secondary' }}>
                      {audioService.formatTime(duration)}
                    </Typography>
                  </Box>
                  <LinearProgress
                    variant="determinate"
                    value={progress}
                    sx={{
                      height: 8,
                      borderRadius: 1,
                      bgcolor: 'grey.200',
                      '& .MuiLinearProgress-bar': {
                        bgcolor: 'success.main',
                        borderRadius: 1
                      }
                    }}
                  />
                </Box>
              </Box>

              {/* Controls */}
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <IconButton size="small" onClick={handlePrevious}>
                  <SkipPreviousIcon />
                </IconButton>
                <IconButton
                  onClick={handlePlayPause}
                  sx={{
                    bgcolor: 'success.main',
                    color: 'white',
                    '&:hover': { bgcolor: 'success.dark' }
                  }}
                >
                  {isPlaying ? <PauseIcon /> : <PlayArrowIcon />}
                </IconButton>
                <IconButton size="small" onClick={handleNext}>
                  <SkipNextIcon />
                </IconButton>
                <IconButton size="small" onClick={handleShuffle}>
                  <ShuffleIcon />
                </IconButton>
                <IconButton size="small" onClick={handleRepeat}>
                  <RepeatIcon />
                </IconButton>
              </Box>
            </Box>
          </Paper>

          {/* Two Column Layout */}
          <Grid container spacing={3}>
            {/* Recent Activity */}
            <Grid item xs={12} lg={6}>
              <Paper sx={{ p: 3, bgcolor: 'background.paper', borderRadius: 3, height: 'fit-content' }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                  <Typography variant="h6" sx={{ fontWeight: 600 }}>
                    Recent Activity
                  </Typography>
                  {formattedActivity.length > activityPageSize && (
                    <Typography variant="caption" sx={{ color: 'text.secondary' }}>
                      {formattedActivity.length} total plays
                    </Typography>
                  )}
                </Box>
                <List sx={{ p: 0, minHeight: '200px' }}>
                  {loading ? (
                    <ListItem sx={{ px: 0, py: 2 }}>
                      <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                        Loading activity...
                      </Typography>
                    </ListItem>
                  ) : paginatedActivity.length === 0 ? (
                    <ListItem sx={{ px: 0, py: 2 }}>
                      <Typography variant="body2" sx={{ color: 'text.black' }}>
                        No recent activity
                      </Typography>
                    </ListItem>
                  ) : (
                    paginatedActivity.map((activity, index) => (
                      <ListItem key={`${activityPage}-${index}`} sx={{ px: 0, py: 1.5 }}>
                        <ListItemIcon sx={{ minWidth: 24 }}>
                          <CircleIcon sx={{ fontSize: 8, color: 'primary.main' }} />
                        </ListItemIcon>
                        <ListItemText
                          primary={
                            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                              <Typography variant="body2" sx={{ fontWeight: 500,  }}>
                                {activity.action}
                              </Typography>
                              <Typography variant="caption" sx={{ color: 'text.secondary' }}>
                                {activity.time}
                              </Typography>
                            </Box>
                          }
                          secondary={
                            <Box>
                              <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                                {activity.detail}
                              </Typography>
                              {activity.duration && (
                                <Typography variant="caption" sx={{ color: 'text.secondary' }}>
                                  Duration: {activity.duration}
                                </Typography>
                              )}
                            </Box>
                          }
                        />
                      </ListItem>
                    ))
                  )}
                </List>

                {/* Pagination */}
                {totalActivityPages > 1 && (
                  <Stack spacing={2} alignItems="center" sx={{ mt: 2 }}>
                    <Pagination
                      count={totalActivityPages}
                      page={activityPage + 1}
                      onChange={handleActivityPageChange}
                      color="primary"
                      size="small"
                    />
                  </Stack>
                )}
              </Paper>
            </Grid>

            {/* Upcoming Schedule */}
            <Grid item xs={12} lg={6}>
              <Paper sx={{ p: 3, bgcolor: 'background.paper', borderRadius: 3, height: 'fit-content' }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                  <Typography variant="h6" sx={{ fontWeight: 600 }}>
                    Upcoming Schedule
                  </Typography>
                  <Button
                    variant="text"
                    size="small"
                    sx={{ textTransform: 'none' }}
                    onClick={handleViewAllSchedule}
                  >
                    View All
                  </Button>
                </Box>
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                  {upcomingSchedule.length > 0 ? (
                    upcomingSchedule.map((item, index) => (
                      <Box
                        key={index}
                        sx={{
                          display: 'flex',
                          justifyContent: 'space-between',
                          alignItems: 'center',
                          p: 2,
                          bgcolor: 'grey.50',
                          borderRadius: 2
                        }}
                      >
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5 }}>
                          <CalendarTodayIcon sx={{ fontSize: 16, color: 'text.secondary' }} />
                          <Box>
                            <Typography variant="body2" sx={{ fontWeight: 500, color: 'black' }}>
                              {item.name}
                            </Typography>
                            <Typography variant="caption" sx={{ color: 'text.secondary' }}>
                              {item.duration}
                            </Typography>
                          </Box>
                        </Box>
                        <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                          {item.time}
                        </Typography>
                      </Box>
                    ))
                  ) : (
                    <Box
                      sx={{
                        display: 'flex',
                        flexDirection: 'column',
                        alignItems: 'center',
                        justifyContent: 'center',
                        p: 4,
                        bgcolor: 'grey.50',
                        borderRadius: 2,
                        textAlign: 'center'
                      }}
                    >
                      <CalendarTodayIcon sx={{ fontSize: 32, color: 'text.secondary', mb: 1 }} />
                      <Typography variant="body2" sx={{ color: 'text.secondary', mb: 1 }}>
                        No upcoming schedules for today
                      </Typography>
                      
                    </Box>
                  )}
                </Box>
              </Paper>
            </Grid>
          </Grid>
        </Box>
      </Box>

      {/* Playlist Dialog */}
      <Dialog
        open={playlistDialogOpen}
        onClose={() => setPlaylistDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="h6">Current Playlist</Typography>
          <IconButton onClick={() => setPlaylistDialogOpen(false)}>
            <CloseIcon />
          </IconButton>
        </DialogTitle>
        <DialogContent>
          <List sx={{ p: 0 }}>
            {currentPlaylist && currentPlaylist.length > 0 ? (
              currentPlaylist.map((track, index) => (
                <ListItemButton
                  key={track._id || index}
                  onClick={() => handlePlayTrack(index)}
                  sx={{
                    borderRadius: 1,
                    mb: 1,
                    bgcolor: audioService.getCurrentIndex() === index ? 'primary.light' : 'transparent',
                    '&:hover': {
                      bgcolor: audioService.getCurrentIndex() === index ? 'primary.main' : 'action.hover'
                    }
                  }}
                >
                  <ListItemIcon>
                    <MusicNoteIcon
                      sx={{
                        color: audioService.getCurrentIndex() === index ? 'primary.main' : 'text.secondary'
                      }}
                    />
                  </ListItemIcon>
                  <ListItemText
                    primary={track.title || 'Unknown Track'}
                    secondary={`${track.artist || 'Unknown Artist'} • ${track.album || 'Unknown Album'}`}
                    primaryTypographyProps={{
                      fontWeight: audioService.getCurrentIndex() === index ? 600 : 400
                    }}
                  />
                  {audioService.getCurrentIndex() === index && (
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <CircleIcon sx={{ fontSize: 8, color: 'primary.main', animation: 'pulse 2s infinite' }} />
                      <Typography variant="caption" sx={{ color: 'primary.main' }}>
                        Playing
                      </Typography>
                    </Box>
                  )}
                </ListItemButton>
              ))
            ) : (
              <ListItem>
                <ListItemText
                  primary="No tracks in playlist"
                  secondary="Add tracks to start playing music"
                />
              </ListItem>
            )}
          </List>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setPlaylistDialogOpen(false)}>Close</Button>
        </DialogActions>
      </Dialog>

      {/* Scheduled Playlist Indicator */}
      <ScheduledPlaylistIndicator />
    </Box>
  );
};

export default MaterialStoreDashboard;
