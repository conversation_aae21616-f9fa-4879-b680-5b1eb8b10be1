// Test file to verify date handling fixes
// This can be run with Node.js to test the date helper functions

// Mock the helper functions from ComplianceReports.jsx
const formatDate = (dateValue, options = {}) => {
  if (!dateValue) return 'N/A';
  try {
    const date = new Date(dateValue);
    if (isNaN(date.getTime())) return 'Invalid Date';
    return options.includeTime ? date.toLocaleString() : date.toLocaleDateString();
  } catch (error) {
    console.error('Error formatting date:', error);
    return 'Invalid Date';
  }
};

const formatDateForInput = (dateValue) => {
  if (!dateValue) return '';
  try {
    const date = new Date(dateValue);
    if (isNaN(date.getTime())) return '';
    return date.toISOString().split('T')[0];
  } catch (error) {
    console.error('Error formatting date for input:', error);
    return '';
  }
};

// Test cases
console.log('Testing date helper functions...\n');

// Test valid dates
console.log('Valid date tests:');
const validDate = new Date('2024-01-15');
console.log('formatDate(valid date):', formatDate(validDate));
console.log('formatDateForInput(valid date):', formatDateForInput(validDate));

// Test invalid dates
console.log('\nInvalid date tests:');
const invalidDate = new Date('invalid');
console.log('formatDate(invalid date):', formatDate(invalidDate));
console.log('formatDateForInput(invalid date):', formatDateForInput(invalidDate));

// Test null/undefined
console.log('\nNull/undefined tests:');
console.log('formatDate(null):', formatDate(null));
console.log('formatDate(undefined):', formatDate(undefined));
console.log('formatDateForInput(null):', formatDateForInput(null));
console.log('formatDateForInput(undefined):', formatDateForInput(undefined));

// Test empty string
console.log('\nEmpty string tests:');
console.log('formatDate(""):', formatDate(''));
console.log('formatDateForInput(""):', formatDateForInput(''));

// Test the specific scenario that was causing the error
console.log('\nScenario that was causing RangeError:');
try {
  const problematicDate = new Date('');
  console.log('new Date("").toISOString() would throw:', 'RangeError: Invalid time value');
  console.log('Our formatDateForInput("") returns:', formatDateForInput(''));
} catch (error) {
  console.log('Error caught:', error.message);
}

console.log('\nAll tests completed successfully! ✅');
